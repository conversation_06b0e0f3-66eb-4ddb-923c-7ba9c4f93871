using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web;

namespace Snp.ePort.Web.Infrastructure.Services
{
    public interface IMSDSGatewayService
    {
        /// <summary>
        /// Upload MSDS files via Gateway API
        /// </summary>
        /// <param name="files">Files to upload</param>
        /// <param name="orderDetailNos">Order detail numbers</param>
        /// <returns>Upload response</returns>
        Task<MSDSUploadGatewayResponse> UploadMSDSFilesAsync(List<HttpPostedFileBase> files, List<string> orderDetailNos);

        /// <summary>
        /// Delete MSDS file via Gateway API
        /// </summary>
        /// <param name="fileId">File ID to delete</param>
        /// <returns>Delete response</returns>
        Task<MSDSDeleteGatewayResponse> DeleteMSDSFileAsync(string fileId);

        /// <summary>
        /// Get MSDS files by order detail number via Gateway API
        /// </summary>
        /// <param name="orderDetailNo">Order detail number</param>
        /// <returns>List of MSDS files</returns>
        Task<List<MSDSFileGatewayInfo>> GetMSDSFilesByOrderDetailNoAsync(string orderDetailNo);
    }

    public class MSDSUploadGatewayResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public List<MSDSUploadedFileGatewayInfo> UploadedFiles { get; set; }

        public MSDSUploadGatewayResponse()
        {
            UploadedFiles = new List<MSDSUploadedFileGatewayInfo>();
        }
    }

    public class MSDSUploadedFileGatewayInfo
    {
        public string FileId { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public long FileSize { get; set; }
        public List<string> LinkedOrderDetails { get; set; }

        public MSDSUploadedFileGatewayInfo()
        {
            LinkedOrderDetails = new List<string>();
        }
    }

    public class MSDSDeleteGatewayResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
    }

    public class MSDSFileGatewayInfo
    {
        public string FileId { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public long FileSize { get; set; }
        public string OrderDetailNo { get; set; }
        public System.DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
    }

    public class MSDSConfigGatewayInfo
    {
        public int MaxFilesPerContainer { get; set; }
        public long MaxTotalSizePerContainer { get; set; }
        public List<string> AllowedExtensions { get; set; }

        public MSDSConfigGatewayInfo()
        {
            AllowedExtensions = new List<string>();
        }
    }
}

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using Snp.ePort.Gateway;

namespace Snp.ePort.Gateway.Impl
{
    public class MSDSService : IMSDSService
    {
        private readonly IMinIOService _minIOService;

        public MSDSService(IMinIOService minIOService)
        {
            _minIOService = minIOService ?? throw new ArgumentNullException(nameof(minIOService));
        }

        public async Task<MSDSUploadResponse> UploadMSDSFilesAsync(MSDSUploadRequest request, MSDSConfigInfo config)
        {
            var response = new MSDSUploadResponse();

            try
            {
                // Validate request
                var validationResult = await ValidateUploadRequest(request, config);
                if (!validationResult.IsValid)
                {
                    response.Success = false;
                    response.Message = validationResult.ErrorMessage;
                    return response;
                }

                // Upload files to MinIO and save metadata
                foreach (var file in request.Files)
                {
                    var fileId = Guid.NewGuid().ToString();
                    var fileName = Path.GetFileName(file.FileName);
                    var filePath = $"msds/{DateTime.Now:yyyy/MM/dd}/{fileId}_{fileName}";

                    // Upload to MinIO
                    var uploadResult = await _minIOService.UploadFileAsync(filePath, file.InputStream, file.ContentType);
                    
                    if (uploadResult.Success)
                    {
                        // Save metadata to database for each order detail
                        foreach (var orderDetailNo in request.OrderDetailNos)
                        {
                            var metadata = new MSDSFileMetadata
                            {
                                FileId = fileId,
                                FileName = fileName,
                                FilePath = filePath,
                                FileSize = file.ContentLength,
                                ContentType = file.ContentType,
                                UploadDate = DateTime.Now,
                                OrderDetailNo = orderDetailNo
                            };

                            //await _eportService.SaveMSDSFileMetadataAsync(metadata);
                        }

                        response.UploadedFiles.Add(new MSDSUploadedFileInfo
                        {
                            FileId = fileId,
                            FileName = fileName,
                            FilePath = filePath,
                            FileSize = file.ContentLength,
                            ContentType = file.ContentType
                        });
                    }
                }

                response.Success = true;
                response.Message = $"Successfully uploaded {response.UploadedFiles.Count} files";
            }
            catch (Exception ex)
            {
                response.Success = false;
                response.Message = $"Upload failed: {ex.Message}";
            }

            return response;
        }

        public async Task<MSDSDeleteResponse> DeleteMSDSFileAsync(string fileId)
        {
            var response = new MSDSDeleteResponse();

            try
            {
                //// Get file metadata to get file path
                //var files = await _eportService.GetMSDSFilesByOrderDetailNoAsync(null); // Get all files
                //var fileToDelete = files.FirstOrDefault(f => f.FileId == fileId);

                //if (fileToDelete == null)
                //{
                //    response.Success = false;
                //    response.Message = "File not found";
                //    return response;
                //}

                //// Delete from MinIO
                //var deleteResult = await _minIOService.DeleteFileAsync(fileToDelete.FilePath);
                
                //if (deleteResult)
                //{
                //    // Delete metadata from database
                //    await _eportService.DeleteMSDSFileMetadataAsync(fileId);
                    
                //    response.Success = true;
                //    response.Message = "File deleted successfully";
                //}
                //else
                //{
                //    response.Success = false;
                //    response.Message = "Failed to delete file from storage";
                //}
            }
            catch (Exception ex)
            {
                response.Success = false;
                response.Message = $"Delete failed: {ex.Message}";
            }

            return response;
        }

        public async Task<List<MSDSFileInfo>> GetMSDSFilesByOrderDetailNoAsync(string orderDetailNo)
        {
            try
            {
                //var files = await _eportService.GetMSDSFilesByOrderDetailNoAsync(orderDetailNo);
                
                //return files.Select(f => new MSDSFileInfo
                //{
                //    FileId = f.FileId,
                //    FileName = f.FileName,
                //    FilePath = f.FilePath,
                //    FileSize = f.FileSize,
                //    ContentType = f.ContentType,
                //    UploadDate = f.UploadDate
                //}).ToList();
            }
            catch (Exception)
            {
                return new List<MSDSFileInfo>();
            }

            return new List<MSDSFileInfo>();
        }

        private async Task<ValidationResult> ValidateUploadRequest(MSDSUploadRequest request, MSDSConfigInfo config)
        {
            if (request == null)
                return new ValidationResult { IsValid = false, ErrorMessage = "Request cannot be null" };

            if (request.Files == null || !request.Files.Any())
                return new ValidationResult { IsValid = false, ErrorMessage = "No files provided" };

            if (request.OrderDetailNos == null || !request.OrderDetailNos.Any())
                return new ValidationResult { IsValid = false, ErrorMessage = "No order detail numbers provided" };

            // Validate file count
            if (request.Files.Count > config.MaxFilesPerContainer)
                return new ValidationResult { IsValid = false, ErrorMessage = $"Maximum {config.MaxFilesPerContainer} files allowed per container" };

            // Validate total size
            var totalSize = request.Files.Sum(f => f.ContentLength);
            if (totalSize > config.MaxTotalSizePerContainer)
                return new ValidationResult { IsValid = false, ErrorMessage = $"Total file size exceeds {config.MaxTotalSizePerContainer / (1024 * 1024)}MB limit" };

            // Validate file extensions
            foreach (var file in request.Files)
            {
                var extension = Path.GetExtension(file.FileName)?.ToLower();
                if (!config.AllowedExtensions.Contains(extension))
                    return new ValidationResult { IsValid = false, ErrorMessage = $"File type {extension} is not allowed" };
            }

            return new ValidationResult { IsValid = true };
        }
    }
}

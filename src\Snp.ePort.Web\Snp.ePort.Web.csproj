﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="..\..\packages\MSBuild.Microsoft.VisualStudio.Web.targets.********\build\MSBuild.Microsoft.VisualStudio.Web.targets.props" Condition="Exists('..\..\packages\MSBuild.Microsoft.VisualStudio.Web.targets.********\build\MSBuild.Microsoft.VisualStudio.Web.targets.props')" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props')" />
  <!--<Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props')" />-->
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{79AB58C5-31CE-4630-B60D-FE5A75F3C303}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Snp.ePort.Web</RootNamespace>
    <AssemblyName>Snp.ePort.Web</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress>
    </Use64BitIISExpress>
    <IISExpressSSLPort>43724</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication>enabled</IISExpressAnonymousAuthentication>
    <IISExpressWindowsAuthentication>disabled</IISExpressWindowsAuthentication>
    <IISExpressUseClassicPipelineMode>false</IISExpressUseClassicPipelineMode>
    <UseGlobalApplicationHostFile />
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <WebGreaseLibPath>..\..\packages\WebGrease.1.5.2\lib</WebGreaseLibPath>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <!--<TypeScriptToolsVersion>2.5</TypeScriptToolsVersion>-->
    <TypeScriptToolsVersion>3.0</TypeScriptToolsVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <LangVersion>6</LangVersion>
    <CodeAnalysisRuleSet>Snp.ePort.Web.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>Snp.ePort.Web.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AjaxControlToolkit, Version=********, Culture=neutral, PublicKeyToken=28f01b0e84b6d53e, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\libs\AjaxControlToolkit.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime, Version=*******, Culture=neutral, PublicKeyToken=eb42632606e9261f, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Antlr.*******\lib\Antlr3.Runtime.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="AspNet.ScriptManager.bootstrap, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\AspNet.ScriptManager.bootstrap.3.3.7\lib\net45\AspNet.ScriptManager.bootstrap.dll</HintPath>
    </Reference>
    <Reference Include="Autofac, Version=*******, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Autofac.4.6.2\lib\net45\Autofac.dll</HintPath>
    </Reference>
    <Reference Include="Autofac.Extras.DynamicProxy">
      <HintPath>..\..\packages\Autofac.Extras.DynamicProxy.4.5.0\lib\net45\Autofac.Extras.DynamicProxy.dll</HintPath>
    </Reference>
    <Reference Include="Autofac.Integration.Mvc, Version=*******, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Autofac.Mvc5.4.0.2\lib\net45\Autofac.Integration.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="Autofac.Integration.Wcf, Version=*******, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Autofac.Wcf.4.0.0\lib\net45\Autofac.Integration.Wcf.dll</HintPath>
    </Reference>
    <Reference Include="Autofac.Multitenant, Version=4.2.0.0, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Autofac.Multitenant.4.2.0\lib\net451\Autofac.Multitenant.dll</HintPath>
    </Reference>
    <Reference Include="AutoMapper, Version=6.2.1.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\..\packages\AutoMapper.6.2.1\lib\net45\AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="CaptchaMvc, Version=2.5.0.0, Culture=neutral, PublicKeyToken=fe46ad421dd3b0e6, processorArchitecture=MSIL">
      <HintPath>..\..\packages\CaptchaMvc.Mvc5.1.5.0\lib\net45-full\CaptchaMvc.dll</HintPath>
    </Reference>
    <Reference Include="Castle.Core">
      <HintPath>..\..\packages\Castle.Core.4.3.1\lib\net45\Castle.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Charts.v22.2.Core, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Drawing.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Data.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.DataAccess.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Docs.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Office.v22.2.Core, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Pdf.v22.2.Core, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.PivotGrid.v22.2.Core, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Printing.v22.2.Core, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.RichEdit.v22.2.Core, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.RichEdit.v22.2.Export, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Images.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Snap.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Sparkline.v22.2.Core, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.DataVisualization.v22.2.Core, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Spreadsheet.v22.2.Core, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Data.Desktop.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Utils.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Web.ASPxThemes.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Web.Bootstrap.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Web.Mvc5.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Web.Resources.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Web.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Charts.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpo.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraBars.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v22.2.Web, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraGauges.v22.2.Core, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraPrinting.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraReports.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraReports.v22.2.Web.WebForms, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraReports.v22.2.Web, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraTreeList.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExtreme.AspNet.Data, Version=2.8.6.0, Culture=neutral, PublicKeyToken=982f5dab1439d0f7, processorArchitecture=MSIL">
      <HintPath>..\..\packages\DevExtreme.AspNet.Data.2.8.6\lib\net452\DevExtreme.AspNet.Data.dll</HintPath>
    </Reference>
    <Reference Include="DevExtreme.AspNet.Mvc, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\libs\DevExtreme\DevExtreme.AspNet.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="DocumentFormat.OpenXml">
      <HintPath>..\..\libs\DocumentFormat.OpenXml.dll</HintPath>
    </Reference>
    <Reference Include="Elasticsearch.Net, Version=6.0.0.0, Culture=neutral, PublicKeyToken=96c599bbe3e70f5d, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Elasticsearch.Net.6.0.0\lib\net46\Elasticsearch.Net.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="EO.Web, Version=8.0.60.2, Culture=neutral, PublicKeyToken=e92353a6bf73fffc, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\libs\EO.Web.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus, Version=4.1.0.0, Culture=neutral, PublicKeyToken=ea159fdaa78159a1, processorArchitecture=MSIL">
      <HintPath>..\..\packages\EPPlus.4.1.0\lib\net40\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.8.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\log4net.2.0.8\lib\net45-full\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.FriendlyUrls, Version=1.0.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\libs\Microsoft.AspNet.FriendlyUrls.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=2.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NPOI, Version=2.3.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NPOI.2.3.0\lib\net40\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML, Version=2.3.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NPOI.2.3.0\lib\net40\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net, Version=2.3.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NPOI.2.3.0\lib\net40\NPOI.OpenXml4Net.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats, Version=2.3.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NPOI.2.3.0\lib\net40\NPOI.OpenXmlFormats.dll</HintPath>
    </Reference>
    <Reference Include="RestSharp, Version=106.2.1.0, Culture=neutral, PublicKeyToken=598062e77f915f75, processorArchitecture=MSIL">
      <HintPath>..\..\packages\RestSharp.106.2.1\lib\net452\RestSharp.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Serilog, Version=2.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Serilog.2.6.0\lib\net46\Serilog.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Formatting.Compact, Version=*******, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Serilog.Formatting.Compact.1.0.0\lib\net45\Serilog.Formatting.Compact.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Formatting.Elasticsearch, Version=0.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Serilog.Formatting.Elasticsearch.7.1.0\lib\net45\Serilog.Formatting.Elasticsearch.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.Elasticsearch, Version=7.1.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Serilog.Sinks.Elasticsearch.7.1.0\lib\net45\Serilog.Sinks.Elasticsearch.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.File, Version=2.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Serilog.Sinks.File.4.0.0\lib\net45\Serilog.Sinks.File.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.PeriodicBatching, Version=2.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Serilog.Sinks.PeriodicBatching.2.1.1\lib\net45\Serilog.Sinks.PeriodicBatching.dll</HintPath>
    </Reference>
    <Reference Include="SixLabors.Fonts, Version=*******, Culture=neutral, PublicKeyToken=d998eea7b14cab13, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SixLabors.Fonts.1.0.0\lib\netstandard2.0\SixLabors.Fonts.dll</HintPath>
    </Reference>
    <Reference Include="SixLabors.ImageSharp, Version=2.0.0.0, Culture=neutral, PublicKeyToken=d998eea7b14cab13, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SixLabors.ImageSharp.2.1.5\lib\netstandard2.0\SixLabors.ImageSharp.dll</HintPath>
    </Reference>
    <Reference Include="SixLabors.ImageSharp.Drawing, Version=*******, Culture=neutral, PublicKeyToken=d998eea7b14cab13, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SixLabors.ImageSharp.Drawing.1.0.0\lib\netstandard2.0\SixLabors.ImageSharp.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="Snp.Notification.Gateway">
      <HintPath>..\..\libs\Snp.Notification.Gateway.dll</HintPath>
    </Reference>
    <Reference Include="System.Activities" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Net.Http.Formatting, Version=5.2.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.WebApi.Client.5.2.4\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=5.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Runtime.CompilerServices.Unsafe.5.0.0\lib\net45\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Text.Encoding.CodePages, Version=5.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Text.Encoding.CodePages.5.0.0\lib\net461\System.Text.Encoding.CodePages.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.ValueTuple.4.4.0\lib\net461\System.ValueTuple.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.Helpers.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Http, Version=5.2.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.WebApi.Core.5.2.4\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Optimization, Version=1.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.Razor.3.2.3\lib\net45\System.Web.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="AspNet.ScriptManager.jQuery">
      <HintPath>..\..\packages\AspNet.ScriptManager.jQuery.1.10.2\lib\net45\AspNet.ScriptManager.jQuery.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ScriptManager.MSAjax">
      <HintPath>..\..\packages\Microsoft.AspNet.ScriptManager.MSAjax.5.0.0\lib\net45\Microsoft.ScriptManager.MSAjax.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ScriptManager.WebForms">
      <HintPath>..\..\packages\Microsoft.AspNet.ScriptManager.WebForms.5.0.0\lib\net45\Microsoft.ScriptManager.WebForms.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Web.Design, Version=2015.1.401.45, Culture=neutral, PublicKeyToken=121fae78165ba3d4, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\libs\Telerik.Web.Design.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Web.UI, Version=2015.3.930.45, Culture=neutral, PublicKeyToken=121fae78165ba3d4, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\libs\Telerik.Web.UI.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Web.UI.Skins, Version=2015.3.930.45, Culture=neutral, PublicKeyToken=121fae78165ba3d4, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\libs\Telerik.Web.UI.Skins.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\..\packages\WebGrease.1.5.2\lib\WebGrease.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_Data\tancang_pri.xml" />
    <Content Include="Content\Bootstrap3\bootstrap.css" />
    <Content Include="Content\Bootstrap3\bootstrap.min.css" />
    <Content Include="Content\Bootstrap5\bootstrap.css" />
    <Content Include="Content\Bootstrap5\custom.css" />
    <Content Include="Content\dx-custom\dx-custom.css" />
    <Content Include="Images\BANNER2024V1.png" />
    <Content Include="Images\logo SNP.png" />
    <Content Include="Images\PanelTdr.png" />
    <Content Include="Content\Css\style.css" />
    <Content Include="Content\Css\smart_wizard.css" />
    <Content Include="Content\Css\smart_wizard_theme_arrows.css" />
    <Content Include="Content\Css\smart_wizard_theme_circles.css" />
    <Content Include="Content\Css\smart_wizard_theme_dots.css" />
    <Content Include="Images\number_3.png" />
    <Content Include="Images\number_1.png" />
    <Content Include="Images\number_2.png" />
    <Content Include="Images\TDR\TDR_Banner_1.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Pages\Admin\AdjustEInvoice.aspx" />
    <Content Include="Pages\Admin\Default.aspx" />
    <Content Include="Pages\Common\PaymentResult.aspx" />
    <Content Include="Pages\UControls\NewContainer.ascx" />
    <Content Include="Pages\UControls\NewShips.ascx" />
    <Content Include="Pages\UControls\Ships.ascx" />
    <Content Include="Scripts\eport\keycloak.js" />
    <Content Include="Scripts\eport\modules\account-management\account-management.js" />
    <Content Include="Scripts\eport\modules\checkin\edit-check-in-account.js" />
    <Content Include="Scripts\eport\modules\commodity\commodity-popup.js" />
    <Content Include="Scripts\eport\modules\commodity\commodity.js" />
    <Content Include="Scripts\eport\modules\ConnectionManagement\connection-management.js" />
    <Content Include="Scripts\eport\modules\ConnectionManagement\group-connect.js" />
    <Content Include="Scripts\eport\modules\ConnectionManagement\user-connection-status-constant.js" />
    <Content Include="Scripts\eport\modules\ConnectionManagement\waiting-accept-connection.js" />
    <Content Include="Scripts\eport\modules\customsdeclaration\inyard.js" />
    <Content Include="Scripts\eport\modules\customs\customs.declaration-via-config.js" />
    <Content Include="Scripts\eport\modules\emptyContainerDelivery\empty-container-delivery-import.js" />
    <Content Include="Scripts\eport\modules\fullContainerDelivery\full-container-delivery-import-THP.js" />
    <Content Include="Scripts\eport\modules\fullContainerDelivery\full-container-delivery-THP.js" />
    <Content Include="Scripts\eport\modules\fullcontainerreceiving\full-container-receivingTHP.js" />
    <Content Include="Scripts\eport\modules\invoice_trans\Adjustment\adjust_invoice-CTL.js" />
    <Content Include="Scripts\eport\modules\invoice_trans\Cancel\cancel-invoice-CTL.js" />
    <Content Include="Scripts\eport\modules\invoice_trans\Cancel\cancel-invoice.js" />
    <Content Include="Scripts\eport\modules\invoice_trans\Replace\Replace-invoice-CTL.js" />
    <Content Include="Scripts\eport\modules\invoice_trans\Replace\replace_invoice.js" />
    <Content Include="Scripts\eport\modules\invoice_trans\Cancel\cancel-invoice.js" />
    <Content Include="Scripts\eport\modules\EdiEmptyDelivery\import-excel-edi-empty-delivery.js" />
    <Content Include="Scripts\eport\modules\emptyContainerDelivery\empty-container-delivery-CTL-optional.js" />
    <Content Include="Scripts\eport\modules\emptyContainerDelivery\empty-container-delivery-site-optional.js" />
    <Content Include="Scripts\eport\modules\emptyContainerDelivery\Lookup\lookup.js" />
    <Content Include="Scripts\eport\modules\EdiEmptyDelivery\edi-empty-delivery.js" />
    <Content Include="Scripts\eport\modules\fullContainerDelivery\edit-cont-discharge-infor.js" />
    <Content Include="Scripts\eport\modules\customs\customs39.js" />
    <Content Include="Scripts\eport\devextreme-util.js" />
    <Content Include="Scripts\eport\modules\edo\master-bill.js" />
    <Content Include="Scripts\eport\modules\home\home.js" />
    <Content Include="Scripts\eport\modules\HazadousMasterInfo\hazadous-master-info.js" />
    <Content Include="Scripts\eport\modules\manageVehiclePersonnel\manage-vehicle-personnel.js" />
    <Content Include="Scripts\eport\modules\movecontainer\movecontainer-extends.js" />
    <Content Include="Scripts\eport\modules\paymentrequest\payment-request.js" />
    <Content Include="Scripts\eport\modules\registervesselexport\register-vessel-export.js" />
    <Content Include="Scripts\eport\modules\registervesselexport\register-vessel-export-booking-number.js" />
    <Content Include="Scripts\eport\modules\registervesselexport\register-vessel-export-customs-declare.js" />
    <Content Include="Scripts\eport\modules\AdminConfigSetting\admin-confing-setting.js" />
    <Content Include="Scripts\eport\modules\Registration\PaymentList\payment-list-CTL.js" />
    <Content Include="Scripts\eport\modules\Registration\PaymentList\payment-list.js" />
    <Content Include="Scripts\eport\modules\Registration\registration.js" />
    <Content Include="Scripts\eport\modules\service-attachment\service-attachment.js" />
    <Content Include="Scripts\eport\modules\user\forgot-password.js" />
    <Content Include="Scripts\eport\modules\checkin\checkininfo.js" />
    <Content Include="Scripts\eport\modules\checkin\checkinonline.js" />
    <Content Include="Scripts\exceljs.js" />
    <Content Include="Scripts\exceljs.min.js" />
    <Content Include="Scripts\FileSaver.js" />
    <Content Include="Scripts\FileSaver.min.js" />
    <Content Include="Scripts\jquery-2.2.3.js" />
    <Content Include="Scripts\jquery-2.2.3.min.js" />
    <Content Include="Scripts\jspdf.plugin.autotable.js" />
    <Content Include="Scripts\jspdf.plugin.autotable.min.js" />
    <Content Include="Scripts\jspdf.umd.js" />
    <Content Include="Scripts\jspdf.umd.min.js" />
    <Content Include="Scripts\localization\dx.messages.ar.js" />
    <Content Include="Scripts\localization\dx.messages.ca.js" />
    <Content Include="Scripts\localization\dx.messages.cs.js" />
    <Content Include="Scripts\localization\dx.messages.el.js" />
    <Content Include="Scripts\localization\dx.messages.es.js" />
    <Content Include="Scripts\localization\dx.messages.fi.js" />
    <Content Include="Scripts\localization\dx.messages.fr.js" />
    <Content Include="Scripts\localization\dx.messages.hu.js" />
    <Content Include="Scripts\localization\dx.messages.it.js" />
    <Content Include="Scripts\localization\dx.messages.lt.js" />
    <Content Include="Scripts\localization\dx.messages.nb.js" />
    <Content Include="Scripts\localization\dx.messages.nl.js" />
    <Content Include="Scripts\localization\dx.messages.pt.js" />
    <Content Include="Scripts\localization\dx.messages.ro.js" />
    <Content Include="Scripts\localization\dx.messages.sl.js" />
    <Content Include="Scripts\localization\dx.messages.sv.js" />
    <Content Include="Scripts\localization\dx.messages.tr.js" />
    <Content Include="Scripts\localization\dx.messages.vi.js" />
    <Content Include="Scripts\localization\dx.messages.zh-tw.js" />
    <Content Include="Scripts\localization\dx.messages.zh.js" />
    <Content Include="Scripts\popper.min.js" />
    <Content Include="Scripts\share\jquery.smartWizard.js" />
    <Content Include="Content\Invoice\BrowserDetectShare.js" />
    <Content Include="Content\Invoice\jquery.PrintArea2.4.1.SNP.js" />
    <Content Include="Content\Invoice\mainSNP.js" />
    <Content Include="Content\jquery-confirm.min.css" />
    <Content Include="Content\quill.snow.css" />
    <Content Include="Content\dx.custom.control.css" />
    <Content Include="Content\style.css" />
    <Content Include="Content\toastr.min.css" />
    <Content Include="Document\Template\CAPR_EirTemplate.xlsx" />
    <Content Include="Images\banner-20200223.png" />
    <Content Include="Images\GNL.png" />
    <Content Include="Images\HIT.jpg" />
    <Content Include="Images\TCI.jpg" />
    <Content Include="Images\TCT.png" />
    <Content Include="Pages\Default.aspx" />
    <Content Include="Pages\Registration\CutSealNew.aspx" />
    <Content Include="Pages\Registration\YardOfServiceCutSeal.aspx" />
    <Content Include="Pages\Registration\YardOfServiceViewer.aspx" />
    <Content Include="Pages\Registration\DVTB.aspx" />
    <Content Include="Pages\Scripts\ImportExcelKBDO.js" />
    <Content Include="Pages\Scripts\KhaiBaoDO.js" />
    <Content Include="Pages\Scripts\DichVuContainerHang.js" />
    <Content Include="Pages\Scripts\ImportExcelDVTB.js" />
    <Content Include="Pages\Scripts\RegexCommon.js" />
    <Content Include="Reports\CTCCConfirmR.aspx" />
    <Content Include="Reports\CTCCR.aspx" />
    <Content Include="Reports\HouseBillR.aspx" />
    <Content Include="Scripts\eport\ajax-util.js" />
    <Content Include="Scripts\eport\modules\BatchNoList\BatchNoList.js" />
    <Content Include="Scripts\eport\modules\cargoreceiving\cargo-receiving-import.js" />
    <Content Include="Scripts\eport\modules\cargoreceiving\cargo-receiving.js" />
    <Content Include="Scripts\eport\modules\checkin\check-in-detail.js" />
    <Content Include="Scripts\eport\modules\checkin\check-in.js" />
    <Content Include="Scripts\eport\modules\checkin\account-check-in.js" />
    <Content Include="Scripts\eport\modules\common-func-inyard.js" />
    <Content Include="Scripts\eport\modules\confirmedshipping\customsconfirm.js" />
    <Content Include="Scripts\eport\modules\confirmedshipping\yardconsolconfirm.js" />
    <Content Include="Scripts\eport\modules\confirmedshipping\lookupbilloflading.js" />
    <Content Include="Scripts\eport\modules\ContainerInfomation\ContainerInfo.js" />
    <Content Include="Scripts\eport\modules\ContInYard\cont-in-yard.js" />
    <Content Include="Scripts\eport\modules\customs\customs.container.cargo.js" />
    <Content Include="Scripts\eport\modules\customs\customs.declaration.js" />
    <Content Include="Scripts\eport\devextreme-component-options.js" />
    <Content Include="Scripts\eport\modules\cutsealservices\cut-seal-services.js" />
    <Content Include="Scripts\eport\modules\cutsealservices\cut-seal-service-excel.js" />
    <Content Include="Scripts\eport\modules\DownloadInvoice\download-invoice.js" />
    <Content Include="Scripts\eport\modules\edi-booking\edi-booking.js" />
    <Content Include="Scripts\eport\modules\edi\edi.js" />
    <Content Include="Scripts\eport\modules\edo\edo.js" />
    <Content Include="Scripts\eport\modules\eir\eir.js" />
    <Content Include="Scripts\eport\modules\emptyContainerDelivery\empty-container-delivery.js" />
    <Content Include="Scripts\eport\modules\emptyContainerDelivery\empty-container-delivery-import-CTL.js" />
    <Content Include="Scripts\eport\modules\emptyContainerReceiving\empty-container-receiving.js" />
    <Content Include="Scripts\eport\modules\fullContainerDelivery\full-container-delivery-import.js" />
    <Content Include="Scripts\eport\modules\fullContainerDelivery\full-container-delivery.js" />
    <Content Include="Scripts\eport\modules\invoiceConvert\invoice-conversion.js" />
    <Content Include="Scripts\eport\modules\invoiceecom\invoiceecom.js" />
    <Content Include="Scripts\eport\modules\invoice_trans\invoice_trans.js" />
    <Content Include="Scripts\eport\modules\ebooking\ebooking.js" />
    <Content Include="Scripts\eport\modules\LoyaltyCrossChecking\integrateloyalty.js" />
    <Content Include="Scripts\eport\modules\LoyaltyCrossChecking\useloyalty.js" />
    <Content Include="Scripts\eport\modules\fullcontainerreceiving\full-container-receiving-import.js" />
    <Content Include="Scripts\eport\modules\fullcontainerreceiving\full-container-receiving.js" />
    <Content Include="Scripts\eport\modules\movecontainer\movecontainer.js" />
    <Content Include="Scripts\eport\modules\paymentlist\payment-list.js" />
    <Content Include="Scripts\eport\modules\order-infor.js" />
    <Content Include="Scripts\eport\modules\checkout\payment.js" />
    <Content Include="Scripts\eport\modules\ReeferMonitorComfirmation\ReeferMonitorConfirmation.js" />
    <Content Include="Scripts\eport\modules\registerpaymentlist\registerpaymentlist.js" />
    <Content Include="Scripts\eport\modules\SearchContainerInformation\SearchContainerInformation.js" />
    <Content Include="Scripts\eport\modules\registeredshipping\registered-shipping.js" />
    <Content Include="Scripts\eport\modules\confirmedshipping\lineoperconfirm.js" />
    <Content Include="Scripts\eport\modules\revenuereport\revenuereport.js" />
    <Content Include="Scripts\eport\modules\ships\ships.js" />
    <Content Include="Scripts\eport\modules\sms\sms.js" />
    <Content Include="Scripts\eport\modules\transportationunit\assign-delivery.js" />
    <Content Include="Scripts\eport\modules\transportationunit\assign-transport-unit.js" />
    <Content Include="Scripts\eport\modules\transportationunit\container-shipping-info-admin.js" />
    <Content Include="Scripts\eport\modules\transportationunit\container-shipping-info-user.js" />
    <Content Include="Scripts\eport\modules\transportationunit\declare-transport-unit-by-batch.js" />
    <Content Include="Scripts\eport\modules\transportationunit\history.js" />
    <Content Include="Scripts\eport\modules\user\user-checkin-info.js" />
    <Content Include="Scripts\eport\modules\user\user-create-edit.js" />
    <Content Include="Scripts\eport\modules\vesselllpodchanges\vessel-port-change-popup.js" />
    <Content Include="Scripts\eport\modules\vesselllpodchanges\registered-vessel-port-change.js" />
    <Content Include="Scripts\eport\modules\vesselllpodchanges\vessel-port-change.js" />
    <Content Include="Scripts\eport\modules\VesselSchedule\VesselSchedule.js" />
    <Content Include="Scripts\eport\modules\ViewCommon\view-common.js" />
    <Content Include="Scripts\eport\modules\ViolateSeaportSrcurity\violate-seaport-security.js" />
    <Content Include="Scripts\eport\modules\yardservices\yard-service-import.js" />
    <Content Include="Scripts\eport\modules\yardservices\yard-services.js" />
    <Content Include="Scripts\eport\progress-bar.js" />
    <Content Include="Scripts\jquery-confirm.min.js" />
    <Content Include="Scripts\jquery.serializejson.min.js" />
    <Content Include="Scripts\eport\app-constants.js" />
    <Content Include="Scripts\eport\message.js" />
    <Content Include="Scripts\lodash.core.min.js" />
    <Content Include="Scripts\lodash.fp.min.js" />
    <Content Include="Scripts\lodash.min.js" />
    <Content Include="Scripts\mapping.fp.js" />
    <Content Include="Scripts\moment.js" />
    <Content Include="Scripts\notify.min.js" />
    <Content Include="Scripts\populate.js" />
    <Content Include="Scripts\quill.js" />
    <Content Include="Scripts\share\create-control.js" />
    <Content Include="Scripts\share\custom.layout.js" />
    <Content Include="Scripts\share\input-validate-type.js" />
    <Content Include="Scripts\toastr.min.js" />
    <Content Include="Content\admin-custom.css" />
    <Content Include="Content\bootstrap-grid.css" />
    <Content Include="Content\bootstrap-grid.min.css" />
    <Content Include="Content\bootstrap-reboot.min.css" />
    <Content Include="Content\bootstrap-theme.css" />
    <Content Include="Content\bootstrap-theme.min.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="Content\Bootstrap\bootstrap.css" />
    <Content Include="Content\Bootstrap\bootstrap.min.css" />
    <Content Include="Content\Css\common.css" />
    <Content Include="Content\Css\CustomAppearance.css" />
    <Content Include="Content\Css\GlobalStyle.css" />
    <Content Include="Content\dx.android5.light.css" />
    <Content Include="Content\dx.common.css" />
    <Content Include="Content\dx.contrast.compact.css" />
    <Content Include="Content\dx.contrast.css" />
    <Content Include="Content\dx.dark.compact.css" />
    <Content Include="Content\dx.dark.css" />
    <Content Include="Content\dx.ios7.default.css" />
    <Content Include="Content\dx.light.compact.css" />
    <Content Include="Content\dx.light.css" />
    <Content Include="Content\dx.spa.css" />
    <Content Include="Content\dx.win10.black.css" />
    <Content Include="Content\dx.win10.white.css" />
    <Content Include="Content\dx.win8.black.css" />
    <Content Include="Content\dx.win8.white.css" />
    <Content Include="Content\font-awesome.css" />
    <Content Include="Content\font-awesome.min.css" />
    <Content Include="Content\Fonts\fontawesome-webfont.svg" />
    <Content Include="Content\Fonts\glyphicons-halflings-regular.svg" />
    <Content Include="Content\jquery.dataTables.min.css" />
    <Content Include="Content\LogIn.css" />
    <Content Include="Content\metis-menu.min.css" />
    <Content Include="Content\sbadmin\sbadmin.css">
      <DependentUpon>sbadmin.less</DependentUpon>
    </Content>
    <Content Include="Content\sbadmin\sbadmin.min.css">
      <DependentUpon>sbadmin.css</DependentUpon>
    </Content>
    <Content Include="Content\Site.css" />
    <Content Include="ErrorPage.html" />
    <Content Include="favicon.ico">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Global.asax" />
    <Content Include="Content\bootstrap.min.css.map" />
    <Content Include="Content\bootstrap.css.map" />
    <Content Include="Content\bootstrap-theme.min.css.map" />
    <Content Include="Content\bootstrap-theme.css.map" />
    <Content Include="Images\404.gif" />
    <Content Include="Images\6208_booking-icon-220.jpg" />
    <Content Include="Images\account.jpg" />
    <Content Include="Images\account.png" />
    <Content Include="Images\acct-success-icon.png" />
    <Content Include="Images\Add.png" />
    <Content Include="Images\Agent.gif" />
    <Content Include="Images\Agent.png" />
    <Content Include="Images\arrow_blue.jpg" />
    <Content Include="Images\banner.png" />
    <Content Include="Images\Bar-chart-32.png" />
    <Content Include="Images\Bar-chart-32_1.png" />
    <Content Include="Images\Bar-chart-32_19x19.png" />
    <Content Include="Images\bg.jpg" />
    <Content Include="Images\bg_left.jpg" />
    <Content Include="Images\booking.png" />
    <Content Include="Images\booking1.png" />
    <Content Include="Images\book_icon.png" />
    <Content Include="Images\Calendar_scheduleHS.png" />
    <Content Include="Images\check-email-icon.png" />
    <Content Include="Images\checkout_icon_sm.gif" />
    <Content Include="Images\clock1.swf" />
    <Content Include="Images\contact.gif" />
    <Content Include="Images\Contact.png" />
    <Content Include="Images\ContactPage.jpg" />
    <Content Include="Images\Container.gif" />
    <Content Include="Images\Container.png" />
    <Content Include="Images\Delete.gif" />
    <Content Include="Images\dg0.gif" />
    <Content Include="Images\dg1.gif" />
    <Content Include="Images\dg2.gif" />
    <Content Include="Images\dg3.gif" />
    <Content Include="Images\dg4.gif" />
    <Content Include="Images\dg5.gif" />
    <Content Include="Images\dg6.gif" />
    <Content Include="Images\dg7.gif" />
    <Content Include="Images\dg8.gif" />
    <Content Include="Images\dg9.gif" />
    <Content Include="Images\dgam.gif" />
    <Content Include="Images\dgc.gif" />
    <Content Include="Images\dgc0.gif" />
    <Content Include="Images\dgh.gif" />
    <Content Include="Images\dgpm.gif" />
    <Content Include="Images\download.gif" />
    <Content Include="Images\favicon.ico" />
    <Content Include="Images\go.gif" />
    <Content Include="Images\Graphique-32.png" />
    <Content Include="Images\HD.htm" />
    <Content Include="Images\HD_files\image001.png" />
    <Content Include="Images\HD_files\image002.png" />
    <Content Include="Images\HD_files\image003.jpg" />
    <Content Include="Images\HD_files\image003.png" />
    <Content Include="Images\HD_files\image004.png" />
    <Content Include="Images\HD_files\image005.png" />
    <Content Include="Images\Intro.jpg" />
    <Content Include="Images\ISSUERBANK.jpg" />
    <Content Include="Images\issuerBankPaylater.jpg" />
    <Content Include="Images\ISSUERBANK_7030_1564.jpg" />
    <Content Include="Images\Logo.gif" />
    <Content Include="Images\Logo.png" />
    <Content Include="Images\Logo2.png" />
    <Content Include="Images\Logo3.png" />
    <Content Include="Images\LogoTanCang.png" />
    <Content Include="Images\MakeOrder.png" />
    <Content Include="Images\new_icon.gif" />
    <Content Include="Images\online_top.jpg" />
    <Content Include="Images\online_top2.jpg" />
    <Content Include="Images\Open.png" />
    <Content Include="Images\OprContactPage.jpg" />
    <Content Include="Images\pic1.png" />
    <Content Include="Images\pic_dangnhap.jpg" />
    <Content Include="Images\quare1.jpg" />
    <Content Include="Images\Refresh.png" />
    <Content Include="Images\refreshButton.png" />
    <Content Include="Images\Save.gif" />
    <Content Include="Images\Save.png" />
    <Content Include="Images\Search.gif" />
    <Content Include="Images\Search.ico" />
    <Content Include="Images\Search16.ico" />
    <Content Include="Images\Search16x16.ico" />
    <Content Include="Images\Search48.png" />
    <Content Include="Images\search482.ico" />
    <Content Include="Images\sort_asc.png" />
    <Content Include="Images\sort_both.png" />
    <Content Include="Images\spacebar.jpg" />
    <Content Include="Images\spacer.gif" />
    <Content Include="Images\start-icon.png" />
    <Content Include="Images\TcLogo copy.jpg" />
    <Content Include="Images\TcLogo.png" />
    <Content Include="Images\TcLogo2.png" />
    <Content Include="Images\TcLogo3.png" />
    <Content Include="Images\ThanhToan.png" />
    <Content Include="Images\tiendotau.png" />
    <Content Include="Images\timer-icon.png" />
    <Content Include="Images\UnderConstruct.jpg" />
    <Content Include="Images\Underconstruction.jpg" />
    <Content Include="Images\Untitled-1 copy.gif" />
    <Content Include="Images\User.jpg" />
    <Content Include="Images\Vessel.gif" />
    <Content Include="Images\Vessel.png" />
    <Content Include="Pages\Common\BienNhan.aspx" />
    <Content Include="Pages\Common\AccountManager.aspx" />
    <Content Include="Pages\Common\HoaDon.aspx" />
    <Content Include="Pages\Common\QuenMatKhau.aspx" />
    <Content Include="Pages\Common\Report.aspx" />
    <Content Include="Pages\Common\TaiHDDT.aspx" />
    <Content Include="Pages\Common\XuatHoaDon.aspx" />
    <Content Include="Pages\Error.aspx" />
    <Content Include="Pages\Operation\AccountManager.aspx" />
    <Content Include="Pages\Operation\agentreports.aspx" />
    <Content Include="Pages\Operation\Browse.aspx" />
    <Content Include="Pages\Operation\Browse1.aspx" />
    <Content Include="Pages\Operation\categories.aspx" />
    <Content Include="Pages\Operation\CFS.aspx" />
    <Content Include="Pages\Operation\ContainerVGM.aspx" />
    <Content Include="Pages\Operation\ContainerVGMImport.aspx" />
    <Content Include="Pages\Operation\continyard.aspx" />
    <Content Include="Pages\Operation\continyardCMS.aspx" />
    <Content Include="Pages\Operation\ContInYard_New.aspx" />
    <Content Include="Pages\Operation\default.aspx" />
    <Content Include="Pages\Operation\dischloadingorder.aspx" />
    <Content Include="Pages\Operation\DocSection.aspx" />
    <Content Include="Pages\Operation\index.master" />
    <Content Include="Pages\Operation\lifecircle.aspx" />
    <Content Include="Pages\Operation\OprOnDuty.aspx" />
    <Content Include="Pages\Operation\recentmoments.aspx" />
    <Content Include="Pages\Operation\recentmoments_new.aspx" />
    <Content Include="Pages\Operation\VesselOprWindow.aspx" />
    <Content Include="Pages\Operation\VesselOprWindow1.aspx" />
    <Content Include="Pages\Registration\AccountManager.aspx" />
    <Content Include="Pages\Registration\BatchNoList.aspx" />
    <Content Include="Pages\Registration\CAPR.aspx" />
    <Content Include="Pages\Registration\CFS.aspx" />
    <Content Include="Pages\Registration\Default.aspx" />
    <Content Include="Pages\Registration\DVCB.aspx" />
    <Content Include="Pages\Registration\GTHA.aspx" />
    <Content Include="Pages\Registration\HBCX.aspx" />
    <Content Include="Pages\Registration\HoaDon.aspx" />
    <Content Include="Pages\Registration\InvoiceEcom.aspx" />
    <Content Include="Pages\Registration\NHAR.aspx" />
    <Content Include="Pages\Registration\ReportViewer.aspx" />
    <Content Include="Document\Template\ContainerVGMTemplate.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Content\bootstrap-reboot.min.css.map" />
    <Content Include="Content\Bootstrap\.scss-lint.yml" />
    <Content Include="Content\Bootstrap\bootstrap-grid.scss" />
    <Content Include="Content\Bootstrap\bootstrap-reboot.scss" />
    <Content Include="Content\Bootstrap\bootstrap.scss" />
    <Content Include="Content\Bootstrap\mixins\_alert.scss" />
    <Content Include="Content\Bootstrap\mixins\_background-variant.scss" />
    <Content Include="Content\Bootstrap\mixins\_badge.scss" />
    <Content Include="Content\Bootstrap\mixins\_border-radius.scss" />
    <Content Include="Content\Bootstrap\mixins\_breakpoints.scss" />
    <Content Include="Content\Bootstrap\mixins\_buttons.scss" />
    <Content Include="Content\Bootstrap\mixins\_cards.scss" />
    <Content Include="Content\Bootstrap\mixins\_clearfix.scss" />
    <Content Include="Content\Bootstrap\mixins\_float.scss" />
    <Content Include="Content\Bootstrap\mixins\_forms.scss" />
    <Content Include="Content\Bootstrap\mixins\_gradients.scss" />
    <Content Include="Content\Bootstrap\mixins\_grid-framework.scss" />
    <Content Include="Content\Bootstrap\mixins\_grid.scss" />
    <Content Include="Content\Bootstrap\mixins\_hover.scss" />
    <Content Include="Content\Bootstrap\mixins\_image.scss" />
    <Content Include="Content\Bootstrap\mixins\_list-group.scss" />
    <Content Include="Content\Bootstrap\mixins\_lists.scss" />
    <Content Include="Content\Bootstrap\mixins\_nav-divider.scss" />
    <Content Include="Content\Bootstrap\mixins\_navbar-align.scss" />
    <Content Include="Content\Bootstrap\mixins\_pagination.scss" />
    <Content Include="Content\Bootstrap\mixins\_reset-text.scss" />
    <Content Include="Content\Bootstrap\mixins\_resize.scss" />
    <Content Include="Content\Bootstrap\mixins\_screen-reader.scss" />
    <Content Include="Content\Bootstrap\mixins\_size.scss" />
    <Content Include="Content\Bootstrap\mixins\_table-row.scss" />
    <Content Include="Content\Bootstrap\mixins\_text-emphasis.scss" />
    <Content Include="Content\Bootstrap\mixins\_text-hide.scss" />
    <Content Include="Content\Bootstrap\mixins\_text-truncate.scss" />
    <Content Include="Content\Bootstrap\mixins\_transforms.scss" />
    <Content Include="Content\Bootstrap\mixins\_visibility.scss" />
    <Content Include="Content\Bootstrap\utilities\_align.scss" />
    <Content Include="Content\Bootstrap\utilities\_background.scss" />
    <Content Include="Content\Bootstrap\utilities\_borders.scss" />
    <Content Include="Content\Bootstrap\utilities\_clearfix.scss" />
    <Content Include="Content\Bootstrap\utilities\_display.scss" />
    <Content Include="Content\Bootstrap\utilities\_flex.scss" />
    <Content Include="Content\Bootstrap\utilities\_float.scss" />
    <Content Include="Content\Bootstrap\utilities\_position.scss" />
    <Content Include="Content\Bootstrap\utilities\_screenreaders.scss" />
    <Content Include="Content\Bootstrap\utilities\_sizing.scss" />
    <Content Include="Content\Bootstrap\utilities\_spacing.scss" />
    <Content Include="Content\Bootstrap\utilities\_text.scss" />
    <Content Include="Content\Bootstrap\utilities\_visibility.scss" />
    <Content Include="Content\Bootstrap\_alert.scss" />
    <Content Include="Content\Bootstrap\_badge.scss" />
    <Content Include="Content\Bootstrap\_breadcrumb.scss" />
    <Content Include="Content\Bootstrap\_button-group.scss" />
    <Content Include="Content\Bootstrap\_buttons.scss" />
    <Content Include="Content\Bootstrap\_card.scss" />
    <Content Include="Content\Bootstrap\_carousel.scss" />
    <Content Include="Content\Bootstrap\_close.scss" />
    <Content Include="Content\Bootstrap\_code.scss" />
    <Content Include="Content\Bootstrap\_custom-forms.scss" />
    <Content Include="Content\Bootstrap\_custom.scss" />
    <Content Include="Content\Bootstrap\_dropdown.scss" />
    <Content Include="Content\Bootstrap\_forms.scss" />
    <Content Include="Content\Bootstrap\_grid.scss" />
    <Content Include="Content\Bootstrap\_images.scss" />
    <Content Include="Content\Bootstrap\_input-group.scss" />
    <Content Include="Content\Bootstrap\_jumbotron.scss" />
    <Content Include="Content\Bootstrap\_list-group.scss" />
    <Content Include="Content\Bootstrap\_media.scss" />
    <Content Include="Content\Bootstrap\_mixins.scss" />
    <Content Include="Content\Bootstrap\_modal.scss" />
    <Content Include="Content\Bootstrap\_nav.scss" />
    <Content Include="Content\Bootstrap\_navbar.scss" />
    <Content Include="Content\Bootstrap\_normalize.scss" />
    <Content Include="Content\Bootstrap\_pagination.scss" />
    <Content Include="Content\Bootstrap\_popover.scss" />
    <Content Include="Content\Bootstrap\_print.scss" />
    <Content Include="Content\Bootstrap\_progress.scss" />
    <Content Include="Content\Bootstrap\_reboot.scss" />
    <Content Include="Content\Bootstrap\_responsive-embed.scss" />
    <Content Include="Content\Bootstrap\_tables.scss" />
    <Content Include="Content\Bootstrap\_tooltip.scss" />
    <Content Include="Content\Bootstrap\_transitions.scss" />
    <Content Include="Content\Bootstrap\_type.scss" />
    <Content Include="Content\Bootstrap\_utilities.scss" />
    <Content Include="Content\Bootstrap\_variables.scss" />
    <Content Include="Content\Fonts\fontawesome-webfont.eot" />
    <Content Include="Content\Fonts\fontawesome-webfont.ttf" />
    <Content Include="Content\Fonts\fontawesome-webfont.woff" />
    <Content Include="Content\Fonts\fontawesome-webfont.woff2" />
    <Content Include="Content\Fonts\FontAwesome.otf" />
    <Content Include="Content\Fonts\glyphicons-halflings-regular.eot" />
    <Content Include="Content\Fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="Content\Fonts\glyphicons-halflings-regular.woff" />
    <Content Include="Content\Fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="Images\ContactPage.psd.2.exclude" />
    <Content Include="Images\ContactPage.psd.exclude" />
    <Content Include="Images\ContainerBookingGuide.pdf" />
    <Content Include="Images\ePort_Huong_dan_dang_ky_giao_nhan_container.pdf" />
    <Content Include="Images\HD_files\themedata.thmx" />
    <Content Include="Images\Intro.psd.exclude" />
    <Content Include="Images\OprContactPage.psd.exclude" />
    <Content Include="Images\pic_dangnhap.psd.exclude" />
    <Content Include="Images\TraCuuThongTin-SNP.pdf" />
    <Content Include="Images\User.psd.exclude" />
    <Content Include="Images\UserGuide.pdf" />
    <Content Include="Areas\Admin\Views\web.config" />
    <Content Include="Areas\Admin\Views\Shared\_AdminLayout.cshtml" />
    <Content Include="Areas\Admin\Views\Shared\_AdminMenu.cshtml" />
    <Content Include="Content\sbadmin\mixins.less" />
    <Content Include="Content\sbadmin\sbadmin.less" />
    <Content Include="Content\sbadmin\variables.less" />
    <Content Include="Areas\Admin\Views\Authorization\User\Create.cshtml" />
    <Content Include="Areas\Admin\Views\Feature\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Feature\Edit.cshtml" />
    <Content Include="Areas\Admin\Views\Feature\Create.cshtml" />
    <Content Include="Areas\Admin\Views\Feature\DeleteMenu.cshtml" />
    <Content Include="Areas\Admin\Views\Authorization\User\Edit.cshtml" />
    <Content Include="Areas\Admin\Views\GroupOperMethod\Create.cshtml" />
    <Content Include="Areas\Admin\Views\GroupOperMethod\Delete.cshtml" />
    <Content Include="Areas\Admin\Views\GroupOperMethod\Edit.cshtml" />
    <Content Include="Areas\Admin\Views\GroupOperMethod\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Role\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Role\Create.cshtml" />
    <Content Include="Areas\Admin\Views\Role\Delete.cshtml" />
    <Content Include="Areas\Admin\Views\Role\Edit.cshtml" />
    <Content Include="Areas\Admin\Views\Role\AssignFeature.cshtml" />
    <Content Include="App_Data\keys\merchant_privkey.pem" />
    <Content Include="App_Data\keys\merchant_pub.pem" />
    <Content Include="App_Data\napas.com.vn.crt" />
    <Content Include="Areas\Admin\Views\TransactionSupport\Index.cshtml" />
    <Content Include="Areas\Admin\Views\TransactionSupport\ExportInvoice.cshtml" />
    <Content Include="Areas\Admin\Views\TopoVesselReport\Index.cshtml" />
    <Content Include="Document\Template\DVCB_Template.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Document\Template\RURU_EirTemplate.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Admin\Views\Authorization\AccountType\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Authorization\AccountType\Create.cshtml" />
    <Content Include="Areas\Admin\Views\Authorization\AccountType\Edit.cshtml" />
    <Content Include="Areas\Admin\Views\Authorization\AccountType\Delete.cshtml" />
    <Content Include="Document\DowloadFiles\HOP DONG SU DUNG DICH VU CANG DIEN TU EPORT.pdf" />
    <Content Include="Document\Template\DVTB_EirTemplate.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Document\Template\TKHQ_Template.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Document\Template\QLHH_Template.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Views\Config\OperMethodAndTruck\Index.cshtml" />
    <Content Include="Views\Config\OperMethodAndTruck\Create.cshtml" />
    <Content Include="Views\Config\OperMethodAndTruck\Edit.cshtml" />
    <Content Include="Document\Template\Mẫu khai báo DO Online.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Admin\Views\_ViewStart.cshtml" />
    <Content Include="Areas\Admin\Views\Shared\_Layout.cshtml" />
    <Content Include="Areas\Admin\Views\InvoiceSerial\Index.cshtml" />
    <Content Include="Areas\Admin\Views\InvoicePattern\Index.cshtml" />
    <Content Include="Areas\Admin\Views\InvoicePattern\Create.cshtml" />
    <Content Include="Areas\Admin\Views\OperMethodSetting\Index.cshtml" />
    <Content Include="Areas\Admin\Views\DiscountSetting\Index.cshtml" />
    <Content Include="Areas\Admin\Views\SiteSetting\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Menu\Index.cshtml" />
    <Content Include="Areas\Admin\Views\SpecialHdlCode\Index.cshtml" />
    <Content Include="Areas\Admin\Views\TransportTypeManagement\TranportTypeIndex.cshtml" />
    <Content Include="Areas\Admin\Views\Announcement\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Announcement\Create.cshtml" />
    <Content Include="Areas\Admin\Views\Announcement\Edit.cshtml" />
    <Content Include="Document\Template\GTHA_EirTemplate.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Document\Template\EDI_Template.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Admin\Views\VesselVoyagePortChangeServices\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Authorization\OperMethodAndTruck\Create.cshtml" />
    <Content Include="Areas\Admin\Views\Authorization\OperMethodAndTruck\Edit.cshtml" />
    <Content Include="Areas\Admin\Views\Authorization\OperMethodAndTruck\Index.cshtml" />
    <Content Include="Configuration\EportConfig.Json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\Admin\Views\EmailTemplate\Create.cshtml" />
    <Content Include="Areas\Admin\Views\EmailTemplate\Edit.cshtml" />
    <Content Include="Areas\Admin\Views\EmailTemplate\Index.cshtml" />
    <Content Include="Areas\Admin\Views\EmailTemplate\_CreateOrUpdate.cshtml" />
    <Content Include="Document\Template\HBCX_EirTemplate.xlsx" />
    <Content Include="Areas\Admin\Views\SiteInfo\Index.cshtml" />
    <Content Include="Document\Template\CutSeal_Template.xlsx" />
    <Content Include="Document\Template\RURU_Template.xlsx" />
    <Content Include="Document\Template\RDKH_Template.xlsx" />
    <Content Include="Areas\Admin\Views\SiteInfo\UpdateAmSiteInfo.cshtml" />
    <Content Include="Areas\Admin\Views\Configuration\Index.cshtml" />
    <Content Include="Configuration\message.config">
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="eo_web.ashx" />
    <Content Include="Areas\Admin\Views\HandleCache\Index.cshtml" />
    <Content Include="Document\Template\BGDV_Template.xlsx" />
    <Content Include="Document\Template\DVVC_Template.xlsx" />
    <Content Include="Document\Template\DVCC_Template.xlsx" />
    <Content Include="Document\Template\Edi_Booking_Template.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Document\Template\Mau dang ky dich vu E-Port 11.03.2016 %28SNP%29 Doanh nghiệp.pdf" />
    <Content Include="Areas\Admin\Views\OperMethod\Index.cshtml" />
    <Content Include="Areas\Admin\Views\OperMethod\Edit.cshtml" />
    <Content Include="Areas\Admin\Views\CategoryGroup\Index.cshtml" />
    <Content Include="Areas\Admin\Views\CfgSysCategoryOperMethod\Create.cshtml" />
    <Content Include="Areas\Admin\Views\CfgSysCategoryOperMethod\Index.cshtml" />
    <Content Include="Areas\Admin\Views\SmsTemplate\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Site\_addSiteParams.cshtml" />
    <Content Include="Areas\Admin\Views\Site\_editSiteParams.cshtml" />
    <Content Include="Areas\Admin\Views\Site\AssignGroupOperMethod.cshtml" />
    <Content Include="Areas\Admin\Views\Site\CreateSite.cshtml" />
    <Content Include="Areas\Admin\Views\Site\Edit.cshtml" />
    <Content Include="Areas\Admin\Views\Site\Delete.cshtml" />
    <Content Include="Areas\Admin\Views\Site\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Site\SiteIndex.cshtml" />
    <Content Include="Areas\Admin\Views\Site\SiteURLPartial.cshtml" />
    <Content Include="Areas\Admin\Views\SystemCategory\AmCategoryGroupIndex.cshtml" />
    <Content Include="Areas\Admin\Views\SystemCategory\AmCategoryIndex.cshtml" />
    <Content Include="Areas\Admin\Views\SystemCategory\CreateSysCategory.cshtml" />
    <Content Include="Areas\Admin\Views\SystemCategory\Index.cshtml" />
    <Content Include="Document\Template\TDRPanel.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Document\Template\HuongDanClosingTime.pdf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Document\Template\HuongDanDangKyXuatTau.pdf" />
    <Content Include="Areas\Admin\Views\ConfigSetting\Index.cshtml" />
    <Content Include="Areas\Admin\Views\ConfigSetting\_PopupCreate.cshtml" />
    <Content Include="Areas\Admin\Views\Categogy\Index.cshtml" />
    <Content Include="Areas\Admin\Views\OperMethod\Create.cshtml" />
    <Content Include="Areas\Admin\Views\EportConfigSetting\Index.cshtml" />
    <Content Include="Pages\Admin\index.master" />
    <Content Include="Pages\Admin\MasterPage.master" />
    <Content Include="Pages\Admin\Web.config" />
    <Content Include="Document\Template\TKHQ_Config_Template.xlsx" />
    <Content Include="Document\Template\DSSTK_Template.xlsx" />
    <Content Include="Content\Fonts\Roboto-300.ttf" />
    <Content Include="Content\Fonts\Roboto-300.woff" />
    <Content Include="Content\Fonts\Roboto-300.woff2" />
    <Content Include="Content\Fonts\Roboto-400.ttf" />
    <Content Include="Content\Fonts\Roboto-400.woff" />
    <Content Include="Content\Fonts\Roboto-400.woff2" />
    <Content Include="Content\Fonts\Roboto-500.ttf" />
    <Content Include="Content\Fonts\Roboto-500.woff" />
    <Content Include="Content\Fonts\Roboto-500.woff2" />
    <Content Include="Content\Fonts\Roboto-700.ttf" />
    <Content Include="Content\Fonts\Roboto-700.woff" />
    <Content Include="Content\Fonts\Roboto-700.woff2" />
    <Content Include="Content\icons\dxicons.woff2" />
    <Content Include="Content\icons\dxiconsmaterial.ttf" />
    <Content Include="Content\icons\dxiconsmaterial.woff" />
    <Content Include="Content\icons\dxiconsmaterial.woff2" />
    <Content Include="Document\Template\HBCX_EirTemplate_Full.xlsx" />
    <Content Include="Document\Template\HBCX_EirTemplate_Other.xlsx" />
    <Content Include="Content\Bootstrap3\.csscomb.json" />
    <Content Include="Content\Bootstrap3\.csslintrc" />
    <Content Include="Content\Bootstrap3\alerts.less" />
    <Content Include="Content\Bootstrap3\badges.less" />
    <Content Include="Content\Bootstrap3\bootstrap.less" />
    <Content Include="Content\Bootstrap3\breadcrumbs.less" />
    <Content Include="Content\Bootstrap3\button-groups.less" />
    <Content Include="Content\Bootstrap3\buttons.less" />
    <Content Include="Content\Bootstrap3\carousel.less" />
    <Content Include="Content\Bootstrap3\close.less" />
    <Content Include="Content\Bootstrap3\code.less" />
    <Content Include="Content\Bootstrap3\component-animations.less" />
    <Content Include="Content\Bootstrap3\dropdowns.less" />
    <Content Include="Content\Bootstrap3\forms.less" />
    <Content Include="Content\Bootstrap3\glyphicons.less" />
    <Content Include="Content\Bootstrap3\grid.less" />
    <Content Include="Content\Bootstrap3\input-groups.less" />
    <Content Include="Content\Bootstrap3\jumbotron.less" />
    <Content Include="Content\Bootstrap3\labels.less" />
    <Content Include="Content\Bootstrap3\list-group.less" />
    <Content Include="Content\Bootstrap3\media.less" />
    <Content Include="Content\Bootstrap3\mixins.less" />
    <Content Include="Content\Bootstrap3\mixins\alerts.less" />
    <Content Include="Content\Bootstrap3\mixins\background-variant.less" />
    <Content Include="Content\Bootstrap3\mixins\border-radius.less" />
    <Content Include="Content\Bootstrap3\mixins\buttons.less" />
    <Content Include="Content\Bootstrap3\mixins\center-block.less" />
    <Content Include="Content\Bootstrap3\mixins\clearfix.less" />
    <Content Include="Content\Bootstrap3\mixins\forms.less" />
    <Content Include="Content\Bootstrap3\mixins\gradients.less" />
    <Content Include="Content\Bootstrap3\mixins\grid-framework.less" />
    <Content Include="Content\Bootstrap3\mixins\grid.less" />
    <Content Include="Content\Bootstrap3\mixins\hide-text.less" />
    <Content Include="Content\Bootstrap3\mixins\image.less" />
    <Content Include="Content\Bootstrap3\mixins\labels.less" />
    <Content Include="Content\Bootstrap3\mixins\list-group.less" />
    <Content Include="Content\Bootstrap3\mixins\nav-divider.less" />
    <Content Include="Content\Bootstrap3\mixins\nav-vertical-align.less" />
    <Content Include="Content\Bootstrap3\mixins\opacity.less" />
    <Content Include="Content\Bootstrap3\mixins\pagination.less" />
    <Content Include="Content\Bootstrap3\mixins\panels.less" />
    <Content Include="Content\Bootstrap3\mixins\progress-bar.less" />
    <Content Include="Content\Bootstrap3\mixins\reset-filter.less" />
    <Content Include="Content\Bootstrap3\mixins\reset-text.less" />
    <Content Include="Content\Bootstrap3\mixins\resize.less" />
    <Content Include="Content\Bootstrap3\mixins\responsive-visibility.less" />
    <Content Include="Content\Bootstrap3\mixins\size.less" />
    <Content Include="Content\Bootstrap3\mixins\tab-focus.less" />
    <Content Include="Content\Bootstrap3\mixins\table-row.less" />
    <Content Include="Content\Bootstrap3\mixins\text-emphasis.less" />
    <Content Include="Content\Bootstrap3\mixins\text-overflow.less" />
    <Content Include="Content\Bootstrap3\mixins\vendor-prefixes.less" />
    <Content Include="Content\Bootstrap3\modals.less" />
    <Content Include="Content\Bootstrap3\navbar.less" />
    <Content Include="Content\Bootstrap3\navs.less" />
    <Content Include="Content\Bootstrap3\normalize.less" />
    <Content Include="Content\Bootstrap3\pager.less" />
    <Content Include="Content\Bootstrap3\pagination.less" />
    <Content Include="Content\Bootstrap3\panels.less" />
    <Content Include="Content\Bootstrap3\popovers.less" />
    <Content Include="Content\Bootstrap3\print.less" />
    <Content Include="Content\Bootstrap3\progress-bars.less" />
    <Content Include="Content\Bootstrap3\responsive-embed.less" />
    <Content Include="Content\Bootstrap3\responsive-utilities.less" />
    <Content Include="Content\Bootstrap3\scaffolding.less" />
    <Content Include="Content\Bootstrap3\tables.less" />
    <Content Include="Content\Bootstrap3\theme.less" />
    <Content Include="Content\Bootstrap3\thumbnails.less" />
    <Content Include="Content\Bootstrap3\tooltip.less" />
    <Content Include="Content\Bootstrap3\type.less" />
    <Content Include="Content\Bootstrap3\utilities.less" />
    <Content Include="Content\Bootstrap3\variables.less" />
    <Content Include="Content\Bootstrap3\wells.less" />
    <Content Include="Images\ePort_Huong_dan_dang_ky_giao_nhan_container_v12024.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Images\ePort_Huong_dan_dang_ky_giao_nhan_container_v12024.pdf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Document\Template\CutSeal_Template_CTL_GNL.xlsx" />
    <Content Include="Document\guide\GIAI THICH CAC THUAT NGU TRONG DICH VU DOI CHUYEN TAU.docx" />
    <Content Include="Document\guide\GIAI THICH CAC THUAT NGU TRONG DICH VU DOI CHUYEN TAU GNL.docx" />
    <Content Include="Images\userGuideConnectAccount.pdf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Document\Template\Import_EDI_Empty.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Document\DowloadFiles\NHAR_EirTemplate.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Document\guide\NHAR_EirTemplate.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Document\Template\NHAR_EirTemplate.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Document\Template\HBCX_EirTemplate_THP.xlsx" />
    <None Include="Properties\PublishProfiles\DefaultProfile.pubxml" />
    <None Include="Scripts\jquery-1.10.2-vsdoc.js" />
    <Content Include="Scripts\jquery.validate-vsdoc.js" />
    <None Include="Scripts\_references.js" />
    <Content Include="Pages\Scripts\BillInfor.js" />
    <Content Include="Pages\Scripts\ImportExcelRURU.js" />
    <Content Include="Pages\Scripts\NhanHang.js" />
    <Content Include="Views\Customs39\CustomsCntrCargo\_Index.cshtml" />
    <Content Include="Views\Customs39\CustomsDeclaration\_Create.cshtml" />
    <Content Include="Views\Customs39\CustomsDeclaration\_Index.cshtml" />
    <Content Include="Views\EDO\MasterBill.cshtml" />
    <Content Include="Views\Home\Index.cshtml" />
    <Content Include="Views\Invoices\InvoiceConversion.cshtml" />
    <Content Include="Views\Invoices\InvoicePartials\_ListOrderDetailNo.cshtml" />
    <Content Include="Xml\NapasResultMessage.xml" />
    <Content Include="Xml\UserMessage.xml" />
    <Content Include="Xml\WebConfig.xml" />
    <None Include="App_Data\PublishProfiles\Eport.pubxml" />
    <Content Include="App_Data\tancang_pri.pem" />
    <Content Include="App_Data\tancang_pub.crt" />
    <Content Include="Scripts\aspnet\dx.aspnet.data.js" />
    <Content Include="Scripts\aspnet\dx.aspnet.mvc.js" />
    <Content Include="Scripts\cldr\event.js" />
    <Content Include="Scripts\cldr\event.min.js" />
    <Content Include="Scripts\cldr\supplemental.js" />
    <Content Include="Scripts\cldr\supplemental.min.js" />
    <Content Include="Scripts\cldr\unresolved.js" />
    <Content Include="Scripts\cldr\unresolved.min.js" />
    <Content Include="Scripts\globalize\currency.js" />
    <Content Include="Scripts\globalize\currency.min.js" />
    <Content Include="Scripts\globalize\date.js" />
    <Content Include="Scripts\globalize\date.min.js" />
    <Content Include="Scripts\globalize\message.js" />
    <Content Include="Scripts\globalize\message.min.js" />
    <Content Include="Scripts\globalize\number.js" />
    <Content Include="Scripts\globalize\number.min.js" />
    <Content Include="Scripts\jquery-3.2.1.js" />
    <Content Include="Scripts\jquery-3.6.4.min.js" />
    <Content Include="Scripts\localization\dx.all.de.js" />
    <Content Include="Scripts\localization\dx.all.ja.js" />
    <Content Include="Scripts\localization\dx.all.ru.js" />
    <Content Include="Scripts\localization\dx.messages.de.js" />
    <Content Include="Scripts\localization\dx.messages.en.js" />
    <Content Include="Scripts\localization\dx.messages.ja.js" />
    <Content Include="Scripts\localization\dx.messages.ru.js" />
    <Content Include="Scripts\localization\dx.mobile.de.js" />
    <Content Include="Scripts\localization\dx.mobile.ja.js" />
    <Content Include="Scripts\localization\dx.mobile.ru.js" />
    <Content Include="Scripts\localization\dx.web.de.js" />
    <Content Include="Scripts\localization\dx.web.ja.js" />
    <Content Include="Scripts\localization\dx.web.ru.js" />
    <Content Include="Scripts\vectormap-data\africa.js" />
    <Content Include="Scripts\vectormap-data\canada.js" />
    <Content Include="Scripts\vectormap-data\eurasia.js" />
    <Content Include="Scripts\vectormap-data\europe.js" />
    <Content Include="Scripts\vectormap-data\usa.js" />
    <Content Include="Scripts\vectormap-data\world.js" />
    <Content Include="Scripts\vectormap-utils\dx.vectormaputils.debug.js" />
    <Content Include="Scripts\vectormap-utils\dx.vectormaputils.js" />
    <Content Include="Scripts\vectormap-utils\dx.vectormaputils.node.js" />
    <Content Include="Areas\Admin\Views\Authorization\User\_SuccessEmailTemplate.cshtml" />
    <Content Include="Content\icons\dxicons.ttf" />
    <Content Include="Content\icons\dxicons.woff" />
    <Content Include="Content\icons\dxiconsios.ttf" />
    <Content Include="Content\icons\dxiconsios.woff" />
    <Content Include="Document\Template\ImportCustomers_Template.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Pages\Scripts\BatchNoList.js" />
    <Content Include="Pages\Scripts\CaptchaContainer.js" />
    <Content Include="Pages\Scripts\Common.js" />
    <Content Include="Pages\Scripts\DanhSachContainer.js" />
    <Content Include="Pages\Scripts\GiaoContainerHang.js" />
    <Content Include="Pages\Scripts\GiaoContainerRong.js" />
    <Content Include="Pages\Scripts\ImportExcelCAPR.js" />
    <Content Include="Pages\Scripts\ImportExcelGTHA.js" />
    <Content Include="Pages\Scripts\ImportExcelHBCX.js" />
    <Content Include="Pages\Scripts\ImportExcelNHAR.js" />
    <Content Include="Pages\Scripts\InvoiceEcom.js" />
    <Content Include="Pages\Scripts\InYardContainer.js" />
    <Content Include="Pages\Scripts\LogIn.js" />
    <Content Include="Pages\Scripts\NhanContainerHang.js" />
    <Content Include="Pages\Scripts\NhanContainerRong.js" />
    <Content Include="Pages\Scripts\NhomNguoiDung.js" />
    <Content Include="Pages\Scripts\QuanLyUser.js" />
    <Content Include="Pages\Scripts\ThanhToanDichVu.js" />
    <Content Include="Pages\Scripts\TransactionList_MAN.js" />
    <Content Include="Pages\Scripts\UserFunctionList.js" />
    <Content Include="Pages\Scripts\UtilityJS.js" />
    <Content Include="Pages\Scripts\VesselOpsCtrl.js" />
    <Content Include="Pages\UControls\AgentReportCtrl.ascx" />
    <Content Include="Pages\UControls\BaseUserControl.ascx" />
    <Content Include="Pages\UControls\BottomPanel.ascx" />
    <Content Include="Pages\UControls\CategoriesCtrl.ascx" />
    <Content Include="Pages\UControls\CFS.ascx" />
    <Content Include="Pages\UControls\Container.ascx" />
    <Content Include="Pages\UControls\InYardContainerCtrl.ascx" />
    <Content Include="Pages\UControls\InYardContainerCtrlCMS.ascx" />
    <Content Include="Pages\UControls\LeftMenu.ascx" />
    <Content Include="Pages\UControls\LeftMenuCust.ascx" />
    <Content Include="Pages\UControls\LeftMenuGeneral.ascx" />
    <Content Include="Pages\UControls\LeftPanel.ascx" />
    <Content Include="Pages\UControls\LifeCicleCtrl.ascx" />
    <Content Include="Pages\UControls\RecentmovementCtrl.ascx" />
    <Content Include="Pages\UControls\RecentmovementCtrl1.ascx" />
    <Content Include="Pages\UControls\ToolbarExport.ascx" />
    <Content Include="Pages\UControls\UCAccountManager.ascx" />
    <Content Include="Pages\UControls\VesselOpsCtrl.ascx" />
    <Content Include="Pages\index.master" />
    <Content Include="Pages\Operation\Web.config" />
    <Content Include="Pages\Registration\Web.config" />
    <None Include="Scripts\jquery-1.10.2.intellisense.js" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Scripts\cldr.js" />
    <Content Include="Scripts\cldr.min.js" />
    <Content Include="Scripts\dx.all.js" />
    <Content Include="Scripts\dx.mobile.js" />
    <Content Include="Scripts\dx.viz-web.js" />
    <Content Include="Scripts\dx.viz.js" />
    <Content Include="Scripts\dx.web.js" />
    <Content Include="Scripts\globalize.js" />
    <Content Include="Scripts\globalize.min.js" />
    <Content Include="Scripts\jquery-1.10.2.js" />
    <Content Include="Scripts\jquery-1.10.2.min.js" />
    <Content Include="Scripts\jquery.validate.js" />
    <Content Include="Scripts\jquery.validate.min.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.min.js" />
    <Content Include="Scripts\jszip.js" />
    <Content Include="Scripts\jszip.min.js" />
    <Content Include="Scripts\libraries\admin-custom.js" />
    <Content Include="Scripts\libraries\jquery.dataTables.min.js" />
    <Content Include="Scripts\libraries\metis-menu.js" />
    <Content Include="Scripts\modernizr-2.6.2.js" />
    <Content Include="Scripts\respond.js" />
    <Content Include="Scripts\respond.min.js" />
    <Content Include="Scripts\WebForms\DetailsView.js" />
    <Content Include="Scripts\WebForms\Focus.js" />
    <Content Include="Scripts\WebForms\GridView.js" />
    <Content Include="Scripts\WebForms\Menu.js" />
    <Content Include="Scripts\WebForms\MenuStandards.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjax.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxApplicationServices.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxComponentModel.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxCore.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxGlobalization.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxHistory.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxNetwork.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxSerialization.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxTimer.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxWebForms.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxWebServices.js" />
    <Content Include="Scripts\WebForms\SmartNav.js" />
    <Content Include="Scripts\WebForms\TreeView.js" />
    <Content Include="Scripts\WebForms\WebForms.js" />
    <Content Include="Scripts\WebForms\WebParts.js" />
    <Content Include="Scripts\WebForms\WebUIValidation.js" />
    <Content Include="Site.Master" />
    <Content Include="ViewSwitcher.ascx" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Bundle.config" />
    <Content Include="Site.Mobile.Master" />
    <Content Include="Xml\WebMessage.json" />
    <Content Include="Views\Notification\Index.cshtml" />
    <Content Include="Views\Notification\Create.cshtml" />
    <Content Include="Views\Notification\Update.cshtml" />
    <Content Include="Snp.ePort.Web.ruleset" />
    <Content Include="Views\PublishInvoice\Index.cshtml" />
    <Content Include="Views\RevenueReport\Index.cshtml" />
    <Content Include="Views\LoyaltyCrossChecking\IntegrateLoyaltyIndex.cshtml" />
    <Content Include="Views\LoyaltyCrossChecking\UseLoyaltyIndex.cshtml" />
    <Content Include="Views\FullContainerDelivery\Index.cshtml" />
    <Content Include="Views\FullContainerDelivery\_ListOrderDetail.cshtml" />
    <Content Include="Views\Shared\_OrderInfo.cshtml" />
    <Content Include="Views\FullContainerDelivery\_ImportExcel.cshtml" />
    <Content Include="Views\FullContainerDelivery\_Register.cshtml" />
    <Content Include="Views\FullContainerDelivery\_PorductInfor.cshtml" />
    <Content Include="Views\FullContainerDelivery\_RegisterService.cshtml" />
    <Content Include="Views\FullContainerDelivery\_SpecialStacking.cshtml" />
    <Content Include="Views\FullContainerDelivery\_StopCont.cshtml" />
    <Content Include="Views\FullContainerDelivery\_TemperatureInformation.cshtml" />
    <Content Include="Views\FullContainerDelivery\_OversizeInfo.cshtml" />
    <Content Include="Views\SearchContainerInformation\Index.cshtml" />
    <Content Include="Views\PaymentList\Index.cshtml" />
    <Content Include="Views\ReeferMonitorConfirmation\Index.cshtml" />
    <Content Include="Views\ReeferMonitorConfirmation\_ControlReeferMonitorConfirmation.cshtml" />
    <Content Include="Views\ReeferMonitorConfirmation\_ListInfoReeferMonitorConfirmation.cshtml" />
    <Content Include="Views\Registrations\DeliveryFullContainer.cshtml" />
    <Content Include="Views\Registrations\SearchContainers.cshtml" />
    <Content Include="Views\RegisterPaymentList\Index.cshtml" />
    <Content Include="Views\Checkout\Cart.cshtml" />
    <Content Include="Views\DownloadInvoice\Index.cshtml" />
    <Content Include="Views\Checkout\_Charged.cshtml" />
    <Content Include="Views\Checkout\_Transaction.cshtml" />
    <Content Include="Views\RegisterPaymentList\Index.cshtml" />
    <Content Include="Views\EmptyContainerReceiving\Index.cshtml" />
    <Content Include="Views\EmptyContainerReceiving\_Register.cshtml" />
    <Content Include="Views\EmptyContainerReceiving\_ImportExcel.cshtml" />
    <Content Include="Views\EmptyContainerDelivery\ImportExcel.cshtml" />
    <Content Include="Views\EmptyContainerDelivery\Index.cshtml" />
    <Content Include="Views\EmptyContainerDelivery\_ListOrderDetail.cshtml" />
    <Content Include="Views\EmptyContainerDelivery\_Register.cshtml" />
    <Content Include="Views\ContainerInformation\Index.cshtml" />
    <Content Include="Views\InvoiceEcom\InvoiceEcom.cshtml" />
    <Content Include="Views\Ships\Index.cshtml" />
    <Content Include="Views\ContainerMovement\PopupPayment.cshtml" />
    <Content Include="Views\EmptyContainerReceiving\_Error.cshtml" />
    <Content Include="Views\FullContainerReceiving\_TimePayment.cshtml" />
    <Content Include="Views\CargoReceiving\_TimePayment.cshtml" />
    <Content Include="Views\VesselSchedule\Index.cshtml" />
    <Content Include="Views\VesselSchedule\_PopupVesselScheduler.cshtml" />
    <Content Include="Views\VesselSchedule\_ViewVesselDetail.cshtml" />
    <Content Include="Views\VesselPortChange\_Search.cshtml" />
    <Content Include="Views\VesselPortChange\_List.cshtml" />
    <Content Include="Views\Shared\_ConfirmPlaceOfDeliveryManual.cshtml" />
    <Content Include="Views\Shared\_ConfirmPlaceOfDeliveryImport.cshtml" />
    <Content Include="Views\Shared\_PopupMoveCont.cshtml" />
    <Content Include="Views\FullContainerReceiving\_OversizeInfo.cshtml" />
    <Content Include="Areas\Admin\Views\SmsTemplate\_CreateOrUpdate.cshtml" />
    <Content Include="Views\ANCB\Index.cshtml" />
    <Content Include="Views\CheckIn\RegisterCheckIn.cshtml" />
    <Content Include="Views\CheckIn\RegisterCheckInDetail.cshtml" />
    <Content Include="Views\CheckIn\Index.cshtml" />
    <Content Include="Views\CheckInInfo\Index.cshtml" />
    <Content Include="Views\CheckIn\_Create.cshtml" />
    <Content Include="Areas\Admin\Views\Authorization\User\_AccountInfor.cshtml" />
    <Content Include="Views\EdiBooking\_EdiBookingImportExcel.cshtml" />
    <Content Include="Views\EdiBooking\_EdiBookingRegister.cshtml" />
    <Content Include="Views\EdiBooking\Index.cshtml" />
    <Content Include="Views\EBooking\Index.cshtml" />
    <Content Include="Views\EBooking\_PopupViewDetail.cshtml" />
    <Content Include="Views\ContainerInyard\Index.cshtml" />
    <Content Include="Views\ContainerInyard\_PartialExportCont.cshtml" />
    <Content Include="Views\ContainerInyard\_PartialImportAndEmptyCont.cshtml" />
    <Content Include="Views\ContainerInyard\_PartialSearch.cshtml" />
    <Content Include="Views\EmptyContainerReceiving\_ConfirmEBooking.cshtml" />
    <Content Include="Views\ContainerInyard\_PartialPopupViewExportCont.cshtml" />
    <Content Include="Views\Home\ForgotPassword.cshtml" />
    <Content Include="Views\AccountManagement\Index.cshtml" />
    <Content Include="Views\AccountManagement\_PhonesRegister.cshtml" />
    <Content Include="Views\Shared\_LayoutReport.cshtml" />
    <Content Include="Views\EmptyContainerDelivery\NharReport.cshtml" />
    <Content Include="Views\EmptyContainerReceiving\CaprReport.cshtml" />
    <Content Include="Views\FullContainerDelivery\HbcxReport.cshtml" />
    <Content Include="Views\FullContainerReceiving\GthaReport.cshtml" />
    <Content Include="Views\CutSealService\CutSealNewReport.cshtml" />
    <Content Include="Views\CargoReceiving\YardOfServiceReport.cshtml" />
    <Content Include="Views\YardOfServices\YardOfServiceReport.cshtml" />
    <Content Include="Views\Registration\PaymentList\InvoiceReport.cshtml" />
    <Content Include="Views\VesselPortChange\CtccReport.cshtml" />
    <Content Include="Views\RegisteredShipping\InvoiceReport.cshtml" />
    <Content Include="Views\ContainerMovement\Report.cshtml" />
    <Content Include="Views\EDO\HouseBillReport.cshtml" />
    <Content Include="Views\Checkout\Payment.cshtml" />
    <Content Include="Views\Shared\_ReportLayout.cshtml" />
    <Content Include="Views\Home\_PopupChangePwdForgotPW.cshtml" />
    <Content Include="Views\ManageVehiclePersonnel\AddSingleVehicePersonnel.cshtml" />
    <Content Include="Views\ManageVehiclePersonnel\ConfirmVehiclePersonnelDecl.cshtml" />
    <Content Include="Views\ManageVehiclePersonnel\EditActiveConfirm.cshtml" />
    <Content Include="Views\ManageVehiclePersonnel\Index.cshtml" />
    <Content Include="Views\ManageVehiclePersonnel\VehiclePersonnelDeclaration.cshtml" />
    <Content Include="Views\AccountManagement\_PopupChangePassword.cshtml" />
    <Content Include="Views\AccountManagement\_PopupChangeEmailManagement.cshtml" />
    <Content Include="Views\EmptyContainerDelivery\ImportExcel-CTL.cshtml" />
    <Content Include="Views\FullContainerReceiving\_RegisterTHP.cshtml" />
    <Content Include="Views\FullContainerReceiving\IndexTHP.cshtml" />
    <Content Include="Views\FullContainerDelivery\_Register_THP.cshtml" />
    <Content Include="Views\FullContainerDelivery\_ImportExcelTHP.cshtml" />
    <Content Include="Views\FullContainerDelivery\_PopupWarningIMO.cshtml" />
    <Content Include="Views\AccountManagement\_PopupChangeMail.cshtml" />
    <Content Include="Views\Home\ForgotPassword - tos-21272.txt" />
    <Content Include="Views\MSDS\Index.cshtml" />
    <None Include="Web.Test.config" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\DependencyRegistration.cs" />
    <Compile Include="App_Start\DevExtremeBundleConfig.cs" />
    <Compile Include="App_Start\ExceptionHandler.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\MapperConfiguration.cs" />
    <Compile Include="Areas\Admin\AdminAreaRegistration.cs" />
    <Compile Include="Areas\Admin\Controllers\AuthorizationController.cs" />
    <Compile Include="Areas\Admin\Controllers\CategogyController.cs" />
    <Compile Include="Areas\Admin\Controllers\CategoryGroupController.cs" />
    <Compile Include="Areas\Admin\Controllers\CfgSysCategoryOperMethodController.cs" />
    <Compile Include="Areas\Admin\Controllers\ConfigSettingController.cs" />
    <Compile Include="Areas\Admin\Controllers\DiscountSettingController.cs" />
    <Compile Include="Areas\Admin\Controllers\EmailTemplateController.cs" />
    <Compile Include="Areas\Admin\Controllers\EportConfigSettingController.cs" />
    <Compile Include="Areas\Admin\Controllers\FeatureController.cs" />
    <Compile Include="Areas\Admin\Controllers\GroupOperMethodController.cs" />
    <Compile Include="Areas\Admin\Controllers\HandleCacheController.cs" />
    <Compile Include="Areas\Admin\Controllers\ConfigurationController.cs" />
    <Compile Include="Areas\Admin\Controllers\InvoicePatternController.cs" />
    <Compile Include="Areas\Admin\Controllers\InvoiceSerialController.cs" />
    <Compile Include="Areas\Admin\Controllers\MenuController.cs" />
    <Compile Include="Areas\Admin\Controllers\AnnouncementController.cs" />
    <Compile Include="Areas\Admin\Controllers\OperMethodController.cs" />
    <Compile Include="Areas\Admin\Controllers\OperMethodSettingController.cs" />
    <Compile Include="Areas\Admin\Controllers\RoleController.cs" />
    <Compile Include="Areas\Admin\Controllers\SiteController.cs" />
    <Compile Include="Areas\Admin\Controllers\SiteInfoController.cs" />
    <Compile Include="Areas\Admin\Controllers\SiteSettingController.cs" />
    <Compile Include="Areas\Admin\Controllers\SmsTemplateController.cs" />
    <Compile Include="Areas\Admin\Controllers\SpecialHdlCodeController.cs" />
    <Compile Include="Areas\Admin\Controllers\SystemCategoryController.cs" />
    <Compile Include="Areas\Admin\Controllers\TransportTypeManagementController.cs" />
    <Compile Include="Areas\Admin\Controllers\VesselVoyagePortChangeServicesController.cs" />
    <Compile Include="Areas\Admin\Models\AmConfig\ConfigSettingSearchViewModel.cs" />
    <Compile Include="Areas\Admin\Models\AmDiscountSetting\AmDiscountSettingViewModel.cs" />
    <Compile Include="Areas\Admin\Models\AmEmailConfig\AmEmailConfig.cs" />
    <Compile Include="Areas\Admin\Models\AmMenu\AmMenuViewModel.cs" />
    <Compile Include="Areas\Admin\Models\AmNotification\AmNotificationViewModel.cs" />
    <Compile Include="Areas\Admin\Models\AmEmailTemplate\AmEmailTemplateViewModel.cs" />
    <Compile Include="Areas\Admin\Models\AmService\AmServiceViewModel.cs" />
    <Compile Include="Areas\Admin\Models\AmSiteInfo\AmSiteInfoViewModel.cs" />
    <Compile Include="Areas\Admin\Models\AmSiteSetting\AmSiteSettingViewModel.cs" />
    <Compile Include="Areas\Admin\Models\AmSpecialHDLCode\AmSpecialHDLCodeViewModel.cs" />
    <Compile Include="Areas\Admin\Models\AmSysCategory\AmCategoryGroupViewModel.cs" />
    <Compile Include="Areas\Admin\Models\AmSysCategory\AmCategoryViewModel.cs" />
    <Compile Include="Areas\Admin\Models\AmSysCategory\AmSysCategoryViewModel.cs" />
    <Compile Include="Areas\Admin\Models\AmTransportType\AmTransportTypeViewModel.cs" />
    <Compile Include="Areas\Admin\Models\EportConfigSetting\EportConfigSettingViewModel.cs" />
    <Compile Include="Areas\Admin\Models\HandleCache\HandleCacheViewModel.cs" />
    <Compile Include="Areas\Admin\Models\InvoicePattern\AmInvoicePatternViewModel.cs" />
    <Compile Include="Areas\Admin\Models\InvoiceSerial\AmInvoiceSerialViewModel.cs" />
    <Compile Include="Areas\Admin\Models\OperMethod\EditOperMethodViewModel.cs" />
    <Compile Include="Areas\Admin\Models\OperMethod\OperMethodSettingViewModel.cs" />
    <Compile Include="Areas\Admin\Models\OperMethod\OperMethodViewModel.cs" />
    <Compile Include="Areas\Admin\Models\Site\CfgSiteParamsModel.cs" />
    <Compile Include="Areas\Admin\Models\Site\CfgSiteServiceURLModel.cs" />
    <Compile Include="Areas\Admin\Models\Site\SiteConfigViewModel.cs" />
    <Compile Include="Areas\Admin\Models\VesselVoyagePortChangeServices\VesVoyPortServicesViewModel.cs" />
    <Compile Include="Captcha\CaptchaUtils.cs" />
    <Compile Include="Captcha\CaptchaResult.cs" />
    <Compile Include="Common\Extentions.cs" />
    <Compile Include="Controllers\AccountManagementController.cs" />
    <Compile Include="Controllers\ANCBController.cs" />
    <Compile Include="Controllers\AssignDeliveryController.cs" />
    <Compile Include="Controllers\AssignTransportController.cs" />
    <Compile Include="Controllers\CheckInController.cs" />
    <Compile Include="Controllers\CommodityController.cs" />
    <Compile Include="Controllers\ContainerInformationController.cs" />
    <Compile Include="Controllers\CheckoutController.cs" />
    <Compile Include="Controllers\ContainerShippingInfoAdminController.cs" />
    <Compile Include="Controllers\ContainerShippingInfoController.cs" />
    <Compile Include="Controllers\ContainerInyardController.cs" />
    <Compile Include="Controllers\CustomsDeclarationController.cs" />
    <Compile Include="Controllers\DeclareTransportUnitByBatchController.cs" />
    <Compile Include="Controllers\DownloadInvoiceController.cs" />
    <Compile Include="Controllers\EdiBookingController.cs" />
    <Compile Include="Controllers\ManageVehiclePersonnelController.cs" />
    <Compile Include="Controllers\MSDSController.cs" />
    <Compile Include="Controllers\PaymentRequestController.cs" />
    <Compile Include="Controllers\HazadousMasterInfoController.cs" />
    <Compile Include="Controllers\EdiEmptyDeliveryController.cs" />
    <Compile Include="Controllers\RegisterVesselExportController.cs" />
    <Compile Include="Controllers\EmptyContainerDeliveryController.cs" />
    <Compile Include="Controllers\EmptyContainerReceivingController.cs" />
    <Compile Include="Controllers\FullContainerDeliveryController.cs" />
    <Compile Include="Controllers\HistoryAssignTransportController.cs" />
    <Compile Include="Controllers\InvoiceEcomController.cs" />
    <Compile Include="Controllers\EBookingController.cs" />
    <Compile Include="Controllers\PaymentListController.cs" />
    <Compile Include="Controllers\PlodConfirmController.cs" />
    <Compile Include="Controllers\PublishInvoiceController.cs" />
    <Compile Include="Controllers\ReeferMonitorConfirmationController.cs" />
    <Compile Include="Controllers\RegisterPaymentListController.cs" />
    <Compile Include="Controllers\SearchContainerInformationController.cs" />
    <Compile Include="Controllers\RevenueReportController.cs" />
    <Compile Include="Controllers\ServiceAttachmentController.cs" />
    <Compile Include="Controllers\ShipsController.cs" />
    <Compile Include="Controllers\SmsController.cs" />
    <Compile Include="Controllers\TransactionController.cs" />
    <Compile Include="Controllers\NotificationController.cs" />
    <Compile Include="Controllers\ConnectionManagementController.cs" />
    <Compile Include="Controllers\VesselScheduleController.cs" />
    <Compile Include="Controllers\ViewCommonController.cs" />
    <Compile Include="Controllers\YardConsolConfirmController.cs" />
    <Compile Include="Controllers\CargoReceivingController.cs" />
    <Compile Include="Controllers\ConfigController.cs" />
    <Compile Include="Areas\Admin\Controllers\TopoVesselReportController.cs" />
    <Compile Include="Areas\Admin\Controllers\TransactionSupportController.cs" />
    <Compile Include="Areas\Admin\Models\AccountType\AccountTypeViewModel.cs" />
    <Compile Include="Controllers\ContDocumentManagementController.cs" />
    <Compile Include="Controllers\CustomsConfirmController.cs" />
    <Compile Include="Controllers\EDIController.cs" />
    <Compile Include="Controllers\EirController.cs" />
    <Compile Include="Controllers\FullContainerReceivingController.cs" />
    <Compile Include="Controllers\InvoiceConversionController.cs" />
    <Compile Include="Controllers\InvoicesController.cs" />
    <Compile Include="Controllers\LineOperConfirmController.cs" />
    <Compile Include="Controllers\LookUpBillOfLadingController.cs" />
    <Compile Include="Controllers\LoyaltyCrossCheckingController.cs" />
    <Compile Include="Controllers\BatchNoListController.cs" />
    <Compile Include="Controllers\RegisteredShippingController.cs" />
    <Compile Include="Controllers\RegisteredVesselPortChangeController.cs" />
    <Compile Include="Controllers\ContainerMovementController.cs" />
    <Compile Include="Controllers\PaymentController.cs" />
    <Compile Include="Controllers\CutSealServiceController.cs" />
    <Compile Include="Controllers\YardOfServicesController.cs" />
    <Compile Include="Controllers\VesselPortChangeController.cs" />
    <Compile Include="Infrastructure\Authentication\Principal\PrincipalExtension.cs" />
    <Compile Include="Infrastructure\Configuration.cs" />
    <Compile Include="Infrastructure\Filters\ThrottleCustomFilter.cs" />
    <Compile Include="Infrastructure\RequestParameterStrategy.cs" />
    <Compile Include="Infrastructure\ClientFactory.cs" />
    <Compile Include="Infrastructure\CacheHelper.cs" />
    <Compile Include="Infrastructure\DownloadInvoice.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Infrastructure\Napas\HttpBaseService.cs" />
    <Compile Include="Infrastructure\Napas\Napas3Service.cs" />
    <Compile Include="Infrastructure\Napas\Transaction.cs" />
    <Compile Include="Infrastructure\SessionBase.cs" />
    <Compile Include="Infrastructure\Throttle\CacheRepository.cs" />
    <Compile Include="Infrastructure\Throttle\IIpAddressParser.cs" />
    <Compile Include="Infrastructure\Throttle\IpAddressParser.cs" />
    <Compile Include="Infrastructure\Throttle\IPAddressRange.cs" />
    <Compile Include="Infrastructure\Throttle\IThrottleRepository.cs" />
    <Compile Include="Infrastructure\Throttle\NginxIpAddressParser.cs" />
    <Compile Include="Infrastructure\Throttle\RateLimit.cs" />
    <Compile Include="Infrastructure\Throttle\RequestIdentity.cs" />
    <Compile Include="Infrastructure\Throttle\ThrottingAttributes.cs" />
    <Compile Include="Infrastructure\Throttle\ThrottleCounter.cs" />
    <Compile Include="Infrastructure\Throttle\ThrottleLogEntry.cs" />
    <Compile Include="Infrastructure\Throttle\ThrottlePolicy.cs" />
    <Compile Include="Infrastructure\Throttle\ThrottlePolicyModel.cs" />
    <Compile Include="Infrastructure\Throttle\ThrottlingFilter.cs" />
    <Compile Include="Infrastructure\Validator\StringArrayRegularExpressionAttribute.cs" />
    <Compile Include="Infrastructure\Validator\ValidatorAttribute.cs" />
    <Compile Include="Infrastructure\Validator\ValidatorNumberTypeAttribute.cs" />
    <Compile Include="Infrastructure\Validator\ValidatorSiteRoleAttribute.cs" />
    <Compile Include="Infrastructure\Services\IMSDSGatewayService.cs" />
    <Compile Include="Infrastructure\Services\MSDSGatewayService.cs" />
    <Compile Include="Models\Captcha\CaptchaDto.cs" />
    <Compile Include="Models\CheckIns\CheckInIndexModel.cs" />
    <Compile Include="Models\CheckIns\DeclarationShipConfigModel.cs" />
    <Compile Include="Models\Common\AccessFailSessionModel.cs" />
    <Compile Include="Models\Common\DAILY_TRANS.cs" />
    <Compile Include="Models\Common\KeyValueViewModel.cs" />
    <Compile Include="Models\Common\MB_TRANSACTION_INFO.cs" />
    <Compile Include="Models\Common\MONTH_TRANS.cs" />
    <Compile Include="Models\Common\REFUND.cs" />
    <Compile Include="Models\Config\CfgOperMethodDeclTranspTrkViewModel.cs" />
    <Compile Include="Areas\Admin\Models\CreateUserViewModel.cs" />
    <Compile Include="Areas\Admin\Models\EditUserViewModel.cs" />
    <Compile Include="Areas\Admin\Models\EditUserFeatureViewModel.cs" />
    <Compile Include="Areas\Admin\Models\Feature\FeatureListViewModel.cs" />
    <Compile Include="Areas\Admin\Models\GroupOperMethod\EditGroupOperMethodViewModel.cs" />
    <Compile Include="Areas\Admin\Models\TransactionSupport\TransactionSupportViewModel.cs" />
    <Compile Include="Controllers\AccompaniedServiceController.cs" />
    <Compile Include="Controllers\AgsController.cs" />
    <Compile Include="Controllers\ContainerController.cs" />
    <Compile Include="Controllers\Customs39Controller.cs" />
    <Compile Include="Controllers\InternalServiceRegistrationController.cs" />
    <Compile Include="Controllers\RegistrationController.cs" />
    <Compile Include="Controllers\SettingController.cs" />
    <Compile Include="Controllers\ServiceContractController.cs" />
    <Compile Include="Controllers\TransportationInfoController.cs" />
    <Compile Include="Controllers\OrderDetailTransportController.cs" />
    <Compile Include="Controllers\TransportController.cs" />
    <Compile Include="Controllers\TruckController.cs" />
    <Compile Include="GlobalSuppressions.cs" />
    <Compile Include="Infrastructure\Authentication\Attributes\Authorize.cs" />
    <Compile Include="Infrastructure\Authentication\Principal\CustomPrincipal.cs" />
    <Compile Include="Infrastructure\Authentication\Principal\ICustomPrincipal.cs" />
    <Compile Include="Infrastructure\Authentication\Resources\AdminResources.cs" />
    <Compile Include="Infrastructure\Authentication\Resources\AuthResources.cs" />
    <Compile Include="Infrastructure\Authentication\Resources\SiteCheckbox.cs" />
    <Compile Include="Infrastructure\Authentication\UtilityMethods.cs" />
    <Compile Include="Infrastructure\BaseController.cs" />
    <Compile Include="Infrastructure\BasePage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Infrastructure\BaseUControls.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Infrastructure\MVCExtensions\HTMLExtensions.cs" />
    <Compile Include="Infrastructure\MVCExtensions\JsonNetResult.cs" />
    <Compile Include="Infrastructure\WebUtils.cs" />
    <Compile Include="Models\Ags\AgsValidatationInfoViewModel.cs" />
    <Compile Include="Models\Common\BatchSelectionViewModel.cs" />
    <Compile Include="Models\Common\ErrorViewModel.cs" />
    <Compile Include="Models\Common\RegistrationViewModel.cs" />
    <Compile Include="Areas\Admin\Models\SiteViewModel.cs" />
    <Compile Include="Areas\Admin\Models\GroupOperMethod\OperMethodGroupViewModel.cs" />
    <Compile Include="Areas\Admin\Models\Role\AssignFeatureViewModel.cs" />
    <Compile Include="Areas\Admin\Models\Role\RoleViewModel.cs" />
    <Compile Include="Areas\Admin\Models\Site\AssignGroupOperMethodViewModel.cs" />
    <Compile Include="Areas\Admin\Models\UserFeatureListViewModel.cs" />
    <Compile Include="Areas\Admin\Models\UserSiteRoleViewModel.cs" />
    <Compile Include="Controllers\CommonController.cs" />
    <Compile Include="Controllers\EDOController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Models\Common\LoginViewModel.cs" />
    <Compile Include="Models\Common\MenuViewModel.cs" />
    <Compile Include="Models\Config\CfgSysCategoryOprMethodViewModel.cs" />
    <Compile Include="Models\ConnectionManagement\ConnectionManagementViewModel.cs" />
    <Compile Include="Models\ContainerInformation\ContainerInformationViewModel.cs" />
    <Compile Include="Models\Container\DocumentContManagement\DocumentContManagementViewModel.cs" />
    <Compile Include="Models\Container\FormTimKiemSoHD.cs" />
    <Compile Include="Models\Container\Yards\ContInYard.cs" />
    <Compile Include="Models\Customer\CustomerVesselViewModel.cs" />
    <Compile Include="Models\Customer\EditCustomerLinerOperViewModel.cs" />
    <Compile Include="Models\Customs39\Customs39ViewModel.cs" />
    <Compile Include="Models\Customs39\CustomsCntrCargoViewModel.cs" />
    <Compile Include="Models\Customs39\CustomsDeclarationViewModel.cs" />
    <Compile Include="Models\DestinationChange\ListPortVesselChangeViewModel.cs" />
    <Compile Include="Models\EDI\OnlineRegistrationEDIViewModel.cs" />
    <Compile Include="Models\EDO\EdiMappingViewModel.cs" />
    <Compile Include="Models\EDO\HouseBillViewModel.cs" />
    <Compile Include="Models\EDO\MasterBillViewModel.cs" />
    <Compile Include="Models\EDO\OnlineRegistrationViewModel.cs" />
    <Compile Include="Models\HazadousMasterInfo\HazadousMasterInfoViewModel.cs" />
    <Compile Include="Models\EmptyContainerDelivery\LookupEmptyContDeliveryViewModel.cs" />
    <Compile Include="Models\InternalServices\InternalServiceViewModel.cs" />
    <Compile Include="Models\InternalServices\OrderListViewModel.cs" />
    <Compile Include="Models\InternalServices\OrderViewModel.cs" />
    <Compile Include="Models\Invoices\InvoiceAdjustmentViewModel.cs" />
    <Compile Include="Models\Invoices\InvoiceCancelAdjustViewModel.cs" />
    <Compile Include="Models\Invoices\InvoiceConversionSearchModel.cs" />
    <Compile Include="Models\Invoices\InvoiceReconcilationViewModel.cs" />
    <Compile Include="Models\Invoices\InvoiceReplaceModel.cs" />
    <Compile Include="Models\Invoices\InvoiceSendErrorNotice.cs" />
    <Compile Include="Models\Invoices\InvoicesHandleViewModel.cs" />
    <Compile Include="Models\Invoices\PublishInvoiceModel.cs" />
    <Compile Include="Models\LoyaltyCrossChecking\IntegrateLoyaltyViewModel.cs" />
    <Compile Include="Models\LoyaltyCrossChecking\UseLoyaltyViewModel.cs" />
    <Compile Include="Models\BatchNoList\BatchNoListViewModel.cs" />
    <Compile Include="Models\OrderDetailService\OrderDetailServiceModel.cs" />
    <Compile Include="Models\Otp\OtpDto.cs" />
    <Compile Include="Models\Registration\OrderDetailTruckViewModel.cs" />
    <Compile Include="Models\Registration\PaymentDetailListExportWithPaymentNLModel.cs" />
    <Compile Include="Models\Registration\PaymentDetailListExportModel.cs" />
    <Compile Include="Models\Registration\PaymentListViewModel.cs" />
    <Compile Include="Models\Registration\TransactionInvoicePaymentExport.cs" />
    <Compile Include="Models\Registration\TruckViewModel.cs" />
    <Compile Include="Models\ServiceContract\RegisterServiceContractViewModel.cs" />
    <Compile Include="Models\AccompaniedService\CreateAccompaniedServiceViewModel.cs" />
    <Compile Include="Models\AccompaniedService\AccompaniedServiceViewModel.cs" />
    <Compile Include="Models\OrderDetailTransport\OrderDetailTransportViewModel.cs" />
    <Compile Include="Models\Setting\SendOtpActiveUserModel.cs" />
    <Compile Include="Models\Ship\ShipModel.cs" />
    <Compile Include="Models\Transport\ContInfoOtmViewModel.cs" />
    <Compile Include="Models\Transport\CreateTransportItemViewModel.cs" />
    <Compile Include="Models\Transport\TransportItemViewModel.cs" />
    <Compile Include="Models\VesselPortChange\RegisteredVesseOutVoyage.cs" />
    <Compile Include="Models\VesselPortChange\VesselByOwnerResponse.cs" />
    <Compile Include="Models\VesselPortChange\VesselPortChangeSearchModel.cs" />
    <Compile Include="Models\VesselPortChange\VesselPortChangeViewModel.cs" />
    <Compile Include="Models\VesselSchedule\VesselScheduleViewModel.cs" />
    <Compile Include="Pages\Admin\AdjustEInvoice.aspx.cs">
      <DependentUpon>AdjustEInvoice.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Admin\AdjustEInvoice.aspx.designer.cs">
      <DependentUpon>AdjustEInvoice.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Admin\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Admin\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Admin\index.master.cs">
      <DependentUpon>index.master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Admin\index.master.designer.cs">
      <DependentUpon>index.master</DependentUpon>
    </Compile>
    <Compile Include="Pages\Admin\MasterPage.master.cs">
      <DependentUpon>MasterPage.master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Admin\MasterPage.master.designer.cs">
      <DependentUpon>MasterPage.master</DependentUpon>
    </Compile>
    <Compile Include="Pages\Common\PaymentResult.aspx.cs">
      <DependentUpon>PaymentResult.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Common\PaymentResult.aspx.designer.cs">
      <DependentUpon>PaymentResult.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Extension\BasePageEx.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Common\AccountManager.aspx.cs">
      <DependentUpon>AccountManager.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Common\AccountManager.aspx.designer.cs">
      <DependentUpon>AccountManager.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Common\BienNhan.aspx.cs">
      <DependentUpon>BienNhan.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Common\BienNhan.aspx.designer.cs">
      <DependentUpon>BienNhan.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Common\HoaDon.aspx.cs">
      <DependentUpon>HoaDon.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Common\HoaDon.aspx.designer.cs">
      <DependentUpon>HoaDon.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Common\QuenMatKhau.aspx.cs">
      <DependentUpon>QuenMatKhau.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Common\QuenMatKhau.aspx.designer.cs">
      <DependentUpon>QuenMatKhau.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Common\Report.aspx.cs">
      <DependentUpon>Report.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Common\Report.aspx.designer.cs">
      <DependentUpon>Report.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Common\TaiHDDT.aspx.cs">
      <DependentUpon>TaiHDDT.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Common\TaiHDDT.aspx.designer.cs">
      <DependentUpon>TaiHDDT.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Common\XuatHoaDon.aspx.cs">
      <DependentUpon>XuatHoaDon.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Common\XuatHoaDon.aspx.designer.cs">
      <DependentUpon>XuatHoaDon.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Error.aspx.cs">
      <DependentUpon>Error.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Error.aspx.designer.cs">
      <DependentUpon>Error.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Extension\Common.cs" />
    <Compile Include="Pages\Extension\PaymentProcessing.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\index.master.cs">
      <DependentUpon>index.master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\index.master.designer.cs">
      <DependentUpon>index.master</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\AccountManager.aspx.cs">
      <DependentUpon>AccountManager.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\AccountManager.aspx.designer.cs">
      <DependentUpon>AccountManager.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\agentreports.aspx.cs">
      <DependentUpon>agentreports.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\agentreports.aspx.designer.cs">
      <DependentUpon>agentreports.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\Browse.aspx.cs">
      <DependentUpon>Browse.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\Browse.aspx.designer.cs">
      <DependentUpon>Browse.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\Browse1.aspx.cs">
      <DependentUpon>Browse1.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\Browse1.aspx.designer.cs">
      <DependentUpon>Browse1.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\categories.aspx.cs">
      <DependentUpon>categories.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\categories.aspx.designer.cs">
      <DependentUpon>categories.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\CFS.aspx.cs">
      <DependentUpon>CFS.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\CFS.aspx.designer.cs">
      <DependentUpon>CFS.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\ContainerVGM.aspx.cs">
      <DependentUpon>ContainerVGM.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\ContainerVGM.aspx.designer.cs">
      <DependentUpon>ContainerVGM.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\ContainerVGMImport.aspx.cs">
      <DependentUpon>ContainerVGMImport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\ContainerVGMImport.aspx.designer.cs">
      <DependentUpon>ContainerVGMImport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\continyard.aspx.cs">
      <DependentUpon>continyard.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\continyard.aspx.designer.cs">
      <DependentUpon>continyard.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\continyardCMS.aspx.cs">
      <DependentUpon>continyardCMS.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\continyardCMS.aspx.designer.cs">
      <DependentUpon>continyardCMS.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\ContInYard_New.aspx.cs">
      <DependentUpon>ContInYard_New.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\ContInYard_New.aspx.designer.cs">
      <DependentUpon>ContInYard_New.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\default.aspx.cs">
      <DependentUpon>default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\default.aspx.designer.cs">
      <DependentUpon>default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\dischloadingorder.aspx.cs">
      <DependentUpon>dischloadingorder.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\dischloadingorder.aspx.designer.cs">
      <DependentUpon>dischloadingorder.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\DocSection.aspx.cs">
      <DependentUpon>DocSection.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\DocSection.aspx.designer.cs">
      <DependentUpon>DocSection.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\index.master.cs">
      <DependentUpon>index.master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\index.master.designer.cs">
      <DependentUpon>index.master</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\lifecircle.aspx.cs">
      <DependentUpon>lifecircle.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\lifecircle.aspx.designer.cs">
      <DependentUpon>lifecircle.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\OprOnDuty.aspx.cs">
      <DependentUpon>OprOnDuty.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\OprOnDuty.aspx.designer.cs">
      <DependentUpon>OprOnDuty.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\recentmoments.aspx.cs">
      <DependentUpon>recentmoments.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\recentmoments.aspx.designer.cs">
      <DependentUpon>recentmoments.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\recentmoments_new.aspx.cs">
      <DependentUpon>recentmoments_new.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\recentmoments_new.aspx.designer.cs">
      <DependentUpon>recentmoments_new.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\VesselOprWindow.aspx.cs">
      <DependentUpon>VesselOprWindow.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\VesselOprWindow.aspx.designer.cs">
      <DependentUpon>VesselOprWindow.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Operation\VesselOprWindow1.aspx.cs">
      <DependentUpon>VesselOprWindow1.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Operation\VesselOprWindow1.aspx.designer.cs">
      <DependentUpon>VesselOprWindow1.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Registration\AccountManager.aspx.cs">
      <DependentUpon>AccountManager.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Registration\AccountManager.aspx.designer.cs">
      <DependentUpon>AccountManager.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Registration\BatchNoList.aspx.cs">
      <DependentUpon>BatchNoList.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Registration\BatchNoList.aspx.designer.cs">
      <DependentUpon>BatchNoList.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Registration\CutSealNew.aspx.cs">
      <DependentUpon>CutSealNew.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Registration\CutSealNew.aspx.designer.cs">
      <DependentUpon>CutSealNew.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Registration\YardOfServiceCutSeal.aspx.cs">
      <DependentUpon>YardOfServiceCutSeal.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Registration\YardOfServiceCutSeal.aspx.designer.cs">
      <DependentUpon>YardOfServiceCutSeal.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Registration\YardOfServiceViewer.aspx.cs">
      <DependentUpon>YardOfServiceViewer.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Registration\YardOfServiceViewer.aspx.designer.cs">
      <DependentUpon>YardOfServiceViewer.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Registration\CAPR.aspx.cs">
      <DependentUpon>CAPR.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Registration\CAPR.aspx.designer.cs">
      <DependentUpon>CAPR.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Registration\CFS.aspx.cs">
      <DependentUpon>CFS.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Registration\CFS.aspx.designer.cs">
      <DependentUpon>CFS.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Registration\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Registration\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Registration\DVCB.aspx.cs">
      <DependentUpon>DVCB.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Registration\DVCB.aspx.designer.cs">
      <DependentUpon>DVCB.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Registration\DVTB.aspx.cs">
      <DependentUpon>DVTB.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Registration\DVTB.aspx.designer.cs">
      <DependentUpon>DVTB.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Registration\GTHA.aspx.cs">
      <DependentUpon>GTHA.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Registration\GTHA.aspx.designer.cs">
      <DependentUpon>GTHA.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Registration\HBCX.aspx.cs">
      <DependentUpon>HBCX.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Registration\HBCX.aspx.designer.cs">
      <DependentUpon>HBCX.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Registration\HoaDon.aspx.cs">
      <DependentUpon>HoaDon.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Registration\HoaDon.aspx.designer.cs">
      <DependentUpon>HoaDon.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Registration\InvoiceEcom.aspx.cs">
      <DependentUpon>InvoiceEcom.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Registration\InvoiceEcom.aspx.designer.cs">
      <DependentUpon>InvoiceEcom.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Registration\NHAR.aspx.cs">
      <DependentUpon>NHAR.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Registration\NHAR.aspx.designer.cs">
      <DependentUpon>NHAR.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\Registration\ReportViewer.aspx.cs">
      <DependentUpon>ReportViewer.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\Registration\ReportViewer.aspx.designer.cs">
      <DependentUpon>ReportViewer.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\AgentReportCtrl.ascx.cs">
      <DependentUpon>AgentReportCtrl.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\AgentReportCtrl.ascx.designer.cs">
      <DependentUpon>AgentReportCtrl.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\BaseUserControl.ascx.cs">
      <DependentUpon>BaseUserControl.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\BaseUserControl.ascx.designer.cs">
      <DependentUpon>BaseUserControl.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\BottomPanel.ascx.cs">
      <DependentUpon>BottomPanel.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\BottomPanel.ascx.designer.cs">
      <DependentUpon>BottomPanel.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\CategoriesCtrl.ascx.cs">
      <DependentUpon>CategoriesCtrl.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\CategoriesCtrl.ascx.designer.cs">
      <DependentUpon>CategoriesCtrl.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\CFS.ascx.cs">
      <DependentUpon>CFS.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\CFS.ascx.designer.cs">
      <DependentUpon>CFS.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\Container.ascx.cs">
      <DependentUpon>Container.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\Container.ascx.designer.cs">
      <DependentUpon>Container.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\InYardContainerCtrl.ascx.cs">
      <DependentUpon>InYardContainerCtrl.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\InYardContainerCtrl.ascx.designer.cs">
      <DependentUpon>InYardContainerCtrl.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\InYardContainerCtrlCMS.ascx.cs">
      <DependentUpon>InYardContainerCtrlCMS.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\InYardContainerCtrlCMS.ascx.designer.cs">
      <DependentUpon>InYardContainerCtrlCMS.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\LeftMenu.ascx.cs">
      <DependentUpon>LeftMenu.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\LeftMenu.ascx.designer.cs">
      <DependentUpon>LeftMenu.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\LeftMenuCust.ascx.cs">
      <DependentUpon>LeftMenuCust.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\LeftMenuCust.ascx.designer.cs">
      <DependentUpon>LeftMenuCust.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\LeftMenuGeneral.ascx.cs">
      <DependentUpon>LeftMenuGeneral.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\LeftMenuGeneral.ascx.designer.cs">
      <DependentUpon>LeftMenuGeneral.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\LeftPanel.ascx.cs">
      <DependentUpon>LeftPanel.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\LeftPanel.ascx.designer.cs">
      <DependentUpon>LeftPanel.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\LifeCicleCtrl.ascx.cs">
      <DependentUpon>LifeCicleCtrl.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\LifeCicleCtrl.ascx.designer.cs">
      <DependentUpon>LifeCicleCtrl.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\NewContainer.ascx.cs">
      <DependentUpon>NewContainer.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\NewContainer.ascx.designer.cs">
      <DependentUpon>NewContainer.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\NewShips.ascx.cs">
      <DependentUpon>NewShips.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\NewShips.ascx.designer.cs">
      <DependentUpon>NewShips.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\RecentmovementCtrl.ascx.cs">
      <DependentUpon>RecentmovementCtrl.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\RecentmovementCtrl.ascx.designer.cs">
      <DependentUpon>RecentmovementCtrl.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\RecentmovementCtrl1.ascx.cs">
      <DependentUpon>RecentmovementCtrl1.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\RecentmovementCtrl1.ascx.designer.cs">
      <DependentUpon>RecentmovementCtrl1.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\Ships.ascx.cs">
      <DependentUpon>Ships.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\Ships.ascx.designer.cs">
      <DependentUpon>Ships.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\ToolbarExport.ascx.cs">
      <DependentUpon>ToolbarExport.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\ToolbarExport.ascx.designer.cs">
      <DependentUpon>ToolbarExport.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\UCAccountManager.ascx.cs">
      <DependentUpon>UCAccountManager.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\UCAccountManager.ascx.designer.cs">
      <DependentUpon>UCAccountManager.ascx</DependentUpon>
    </Compile>
    <Compile Include="Pages\UControls\VesselOpsCtrl.ascx.cs">
      <DependentUpon>VesselOpsCtrl.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pages\UControls\VesselOpsCtrl.ascx.designer.cs">
      <DependentUpon>VesselOpsCtrl.ascx</DependentUpon>
    </Compile>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\MSGResource.Designer.cs" />
    <Compile Include="Reports\CTCCConfirmR.aspx.cs">
      <DependentUpon>CTCCConfirmR.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\CTCCConfirmR.aspx.designer.cs">
      <DependentUpon>CTCCConfirmR.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\CTCCR.aspx.cs">
      <DependentUpon>CTCCR.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\CTCCR.aspx.designer.cs">
      <DependentUpon>CTCCR.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\HouseBillR.aspx.cs">
      <DependentUpon>HouseBillR.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\HouseBillR.aspx.designer.cs">
      <DependentUpon>HouseBillR.aspx</DependentUpon>
    </Compile>
    <Compile Include="Sessions\BillingSession.cs" />
    <Compile Include="Sessions\CommonSession.cs" />
    <Compile Include="Sessions\RegisterPaymentSession.cs" />
    <Compile Include="Sessions\VesselScheduleSession.cs" />
    <Compile Include="Site.Master.cs">
      <DependentUpon>Site.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Site.Master.designer.cs">
      <DependentUpon>Site.Master</DependentUpon>
    </Compile>
    <Compile Include="Site.Mobile.Master.cs">
      <DependentUpon>Site.Mobile.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Site.Mobile.Master.designer.cs">
      <DependentUpon>Site.Mobile.Master</DependentUpon>
    </Compile>
    <Compile Include="ViewSwitcher.ascx.cs">
      <DependentUpon>ViewSwitcher.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ViewSwitcher.ascx.designer.cs">
      <DependentUpon>ViewSwitcher.ascx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_GlobalResources\" />
    <Folder Include="Areas\Admin\Models\Registration\" />
    <Folder Include="Document\UploadFiles\" />
    <Folder Include="Document\Xml\" />
    <Folder Include="Views\EmptyRecivSevices\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
    <Content Include="Scripts\jquery-1.10.2.min.map" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\web.config" />
    <Content Include="Areas\Admin\Views\Authorization\Index.cshtml" />
    <Content Include="Views\Common\ErrorPage.cshtml" />
    <Content Include="Views\Shared\_Menu.cshtml" />
    <Content Include="Views\Shared\_Login.cshtml" />
    <Content Include="Views\EDO\HouseBill.cshtml" />
    <Content Include="Views\EDO\_DetailsHouseBill.cshtml" />
    <Content Include="Views\EDO\_HouseBillSelectList.cshtml" />
    <Content Include="Views\EDO\_AssignToHouseBill.cshtml" />
    <Content Include="Views\EDO\_LineOperSelectList.cshtml" />
    <Content Include="Views\Transport\Index.cshtml" />
    <Content Include="Views\Transport\Create.cshtml" />
    <Content Include="Views\Transport\_VesselSelectList.cshtml" />
    <Content Include="Views\Transport\Edit.cshtml" />
    <Content Include="Views\Transport\Delete.cshtml" />
    <Content Include="Views\InternalServiceRegistration\Index.cshtml" />
    <Content Include="Views\InternalServiceRegistration\Register.cshtml" />
    <Content Include="Views\ServiceContract\Register.cshtml" />
    <Content Include="Views\ServiceContract\Unregister.cshtml" />
    <Content Include="Views\ServiceContract\SearchHistory.cshtml" />
    <Content Include="Views\Ags\Index.cshtml" />
    <Content Include="Views\Ags\AlertPartial.cshtml" />
    <Content Include="Views\AccompaniedService\Create.cshtml" />
    <Content Include="Views\AccompaniedService\Edit.cshtml" />
    <Content Include="Views\AccompaniedService\Index.cshtml" />
    <Content Include="Views\TransportationInfo\Index.cshtml" />
    <Content Include="Views\Customs39\Index.cshtml" />
    <Content Include="Views\Common\BatchSelection.cshtml" />
    <Content Include="Views\Customs39\CustomsDeclaration\_Edit.cshtml" />
    <Content Include="Views\Customs39\CustomsCntrCargo\_Create.cshtml" />
    <Content Include="Views\Customs39\CustomsCntrCargo\_Edit.cshtml" />
    <Content Include="Views\Container\Index.cshtml" />
    <Content Include="Views\Customs39\Inquiry.cshtml" />
    <Content Include="Views\Container\_SoToKhai.cshtml" />
    <Content Include="Views\Container\_SoQuanLyHangHoa.cshtml" />
    <Content Include="Views\Customs39\CustomsDeclaration\_BulkEdit.cshtml" />
    <Content Include="Views\Customs39\CustomsDeclaration\_BulkCreate.cshtml" />
    <Content Include="Views\Customs39\CustomsCntrCargo\_BulkCreate.cshtml" />
    <Content Include="Views\Customs39\CustomsCntrCargo\_BulkEdit.cshtml" />
    <Content Include="Views\Customs39\Collate.cshtml" />
    <Content Include="Views\Customs39\CustomsDeclaration\_ImportExcel.cshtml" />
    <Content Include="Views\Customs39\CustomsCntrCargo\_ImportExcel.cshtml" />
    <Content Include="Views\Truck\Index.cshtml" />
    <Content Include="Views\Truck\_Create.cshtml" />
    <Content Include="Views\Truck\_Edit.cshtml" />
    <Content Include="Views\OrderDetailTransport\Index.cshtml" />
    <Content Include="Views\Registration\Index.cshtml" />
    <Content Include="Views\Registration\_Edit.cshtml" />
    <Content Include="Views\Registration\_UpdateAll.cshtml" />
    <Content Include="Views\Truck\Agreement.cshtml" />
    <Content Include="Views\Truck\TruckAdmin.cshtml" />
    <Content Include="Views\EDO\OnlineRegistration.cshtml" />
    <Content Include="Views\EDO\_OnlineRegistration_ImportExcel.cshtml" />
    <Content Include="Views\EDO\_OnlineRegistration_CU.cshtml" />
    <Content Include="Views\Invoices\ErrorTransactionAndErrorInvoiceHandler.cshtml" />
    <Content Include="Views\Invoices\ReplaceInvoice.cshtml" />
    <Content Include="Views\Invoices\InvoiceCancel.cshtml" />
    <Content Include="Views\Invoices\_TransactionHandler.cshtml" />
    <Content Include="Views\Invoices\InvoicesAdjustment.cshtml" />
    <Content Include="Views\Invoices\InvoicePartials\_InvoicesCharges.cshtml" />
    <Content Include="Views\Invoices\InvoicePartials\_InvoicesCancelAndAdjustment.cshtml" />
    <Content Include="Views\Config\StopContainer\IndexStopCont.cshtml" />
    <Content Include="Views\Config\StopContainer\_CreateStopCont.cshtml" />
    <Content Include="Views\EDO\EdiMapping.cshtml" />
    <Content Include="Views\Home\PagesError.cshtml" />
    <Content Include="Views\Registration\PaymentList\Index.cshtml" />
    <Content Include="Views\Invoices\InvoiceReconcilation.cshtml" />
    <Content Include="Views\VesselPortChange\Index.cshtml" />
    <Content Include="Views\RegisteredVesselPortChange\Index.cshtml" />
    <Content Include="Views\VesselPortChange\_VesselPortChange.cshtml" />
    <Content Include="Views\EDI\OnlineRegistrationEDI.cshtml" />
    <Content Include="Views\EDI\_OnlineRegistrationEDI_CU.cshtml" />
    <Content Include="Views\EDO\PopupViewHouseBill.cshtml" />
    <Content Include="Views\EDI\_OnlineRegistration_ImportExcel.cshtml" />
    <Content Include="Views\EDI\_MultipleEdit.cshtml" />
    <Content Include="Views\YardOfServices\Index.cshtml" />
    <Content Include="Views\YardOfServices\YardServiceRegister.cshtml" />
    <Content Include="Views\YardOfServices\_YardOfServiceImport.cshtml" />
    <Content Include="Views\YardOfServices\_OperMethod.cshtml" />
    <Content Include="Views\YardOfServices\_YardOfServiceApproval.cshtml" />
    <Content Include="Views\ContainerMovement\Index.cshtml" />
    <Content Include="Views\ContainerMovement\PopupRegisterContainer.cshtml" />
    <Content Include="Views\ContainerMovement\PopupRegisterContainerExcel.cshtml" />
    <Content Include="Views\CargoReceiving\Index.cshtml" />
    <Content Include="Views\CargoReceiving\_Approval.cshtml" />
    <Content Include="Views\CargoReceiving\_ImportExcel.cshtml" />
    <Content Include="Views\CargoReceiving\_ServiceRegister.cshtml" />
    <Content Include="Views\CargoReceiving\_StopCont.cshtml" />
    <Content Include="Views\Setting\DestroyOrModifyEInvoice.cshtml" />
    <Content Include="Views\Setting\Discount.cshtml" />
    <Content Include="Views\Setting\Index.cshtml" />
    <Content Include="Views\Setting\ProcessErrorTransaction.cshtml" />
    <Content Include="Views\Setting\ReplaceInvoice.cshtml" />
    <Content Include="Views\Setting\Site.cshtml" />
    <Content Include="Views\Truck\AgreementApprovalForTransportationAgent.cshtml" />
    <Content Include="Views\Truck\TruckTransport.cshtml" />
    <Content Include="Views\Eir\Index.cshtml" />
    <Content Include="Views\FullContainerReceiving\Index.cshtml" />
    <Content Include="Views\FullContainerReceiving\_Approval.cshtml" />
    <Content Include="Views\FullContainerReceiving\_ImportExcel.cshtml" />
    <Content Include="Views\FullContainerReceiving\_Register.cshtml" />
    <Content Include="Views\FullContainerReceiving\_StopCont.cshtml" />
    <Content Include="Views\ConfirmedShipping\Index.cshtml" />
    <Content Include="Views\ConfirmedShipping\_Search.cshtml" />
    <Content Include="Views\FullContainerReceiving\_RegisterByBill.cshtml" />
    <Content Include="Views\RegisteredShipping\Index.cshtml" />
    <Content Include="Views\BatchNoList\Index.cshtml" />
    <Content Include="Views\FullContainerReceiving\_Services.cshtml" />
    <Content Include="Views\Container\DocumentContManagement\IndexDocumentCont.cshtml" />
    <Content Include="Views\Shared\_EmptyLayout.cshtml" />
    <Content Include="Views\ConfirmedShipping\_PopupPolicy.cshtml" />
    <Content Include="Views\RegisteredShipping\_PopupRegisterContainer.cshtml" />
    <Content Include="Views\ConfirmedShipping\_PopupLineOperConfirm.cshtml" />
    <Content Include="Views\ConfirmedShipping\_PopupLineOperCancel.cshtml" />
    <Content Include="Views\ConfirmedShipping\_PopupConfirmError.cshtml" />
    <Content Include="Views\ConfirmedShipping\_PopupRejectError.cshtml" />
    <Content Include="Views\ConfirmedShipping\_PopupCancelError.cshtml" />
    <Content Include="Views\FullContainerReceiving\_Warning.cshtml" />
    <Content Include="Views\FullContainerReceiving\_Error.cshtml" />
    <Content Include="Views\RegisteredShipping\_PopupRegistedShippingExcel.cshtml" />
    <Content Include="Views\ConfirmedShipping\LookUpBillOfLading.cshtml" />
    <Content Include="Views\ConfirmedShipping\CustomsConfirm.cshtml" />
    <Content Include="Views\ConfirmedShipping\_PopupCustomsConfirm.cshtml" />
    <Content Include="Views\ConfirmedShipping\_PopupCustomsCancel.cshtml" />
    <Content Include="Views\CutSealService\Index.cshtml" />
    <Content Include="Views\EDO\PopupConfirmStopCont.cshtml" />
    <Content Include="Views\CutSealService\_CutSealServiceRegister.cshtml" />
    <Content Include="Views\CutSealService\_CutSealServiceApproval.cshtml" />
    <Content Include="Views\CutSealService\_CutSealServiceExcel.cshtml" />
    <Content Include="Views\RegisteredShipping\_PopupErrorDebit.cshtml" />
    <Content Include="Views\Shared\_TimeLine.cshtml" />
    <Content Include="Views\Shared\_ErrorInfo.cshtml" />
    <Content Include="Views\ConfirmedShipping\YardConsolConfirm.cshtml" />
    <Content Include="Views\ConfirmedShipping\_PopupYardConsolCancel.cshtml" />
    <Content Include="Views\ConfirmedShipping\_PopupYardConsolConfirm.cshtml" />
    <Content Include="Views\Shared\_ConfirmInfo.cshtml" />
    <Content Include="Views\Shared\_MultiUpdateConfirm.cshtml" />
    <Content Include="Views\CutSealService\_MultiUpdateConfirm.cshtml" />
    <Content Include="Views\TransportationUnit\Index.cshtml" />
    <Content Include="Views\TransportationUnit\PopupDeclareTransport.cshtml" />
    <Content Include="Views\TransportationUnit\BatchNoList\Index.cshtml" />
    <Content Include="Views\TransportationUnit\AssignTransport\AssignTransportUnit.cshtml" />
    <Content Include="Views\TransportationUnit\AssignTransport\AssignDelivery.cshtml" />
    <Content Include="Views\TransportationUnit\Information\ContainerShippingInfo.cshtml" />
    <Content Include="Views\TransportationUnit\Information\ContainerShippingInfoAdmin.cshtml" />
    <Content Include="Views\TransportationUnit\Information\History.cshtml" />
    <Content Include="Views\TransportationUnit\Information\HistoryMasterDetail.cshtml" />
    <Content Include="Views\RegisterVesselExport\Index.cshtml" />
    <Content Include="Views\RegisterVesselExport\Report.cshtml" />
    <Content Include="Views\Shared\Billing\_ForcedContainers.cshtml" />
    <Content Include="Views\Shared\Billing\_MandatoryDeclaration.cshtml" />
    <Content Include="Views\Shared\Billing\_RemindPayment.cshtml" />
    <Content Include="Views\Shared\_Billing.cshtml" />
    <Content Include="Views\RegisterVesselExport\BookingNumber\_Index.cshtml" />
    <Content Include="Views\RegisterVesselExport\CustomsDeclare\_Index.cshtml" />
    <Content Include="Views\RegisterVesselExport\BookingNumber\_PopupConfirmOrder.cshtml" />
    <Content Include="Views\RegisterVesselExport\BookingNumber\_PopupContainerError.cshtml" />
    <Content Include="Views\RegisterVesselExport\BookingNumber\_PopupTermConfirm.cshtml" />
    <Content Include="Views\RegisterVesselExport\BookingNumber\_PopupSmsEmailRegister.cshtml" />
    <Content Include="Views\RegisterVesselExport\BookingNumber\_PopupInvalidContainer.cshtml" />
    <Content Include="Views\RegisterVesselExport\CustomsDeclare\_PopupTermConfirm.cshtml" />
    <Content Include="Views\RegisterVesselExport\CustomsDeclare\_PopupSmsEmailRegister.cshtml" />
    <Content Include="Views\RegisterVesselExport\CustomsDeclare\_PopupInvalidContainer.cshtml" />
    <Content Include="Views\RegisterVesselExport\CustomsDeclare\_PopupContainerError.cshtml" />
    <Content Include="Views\RegisterVesselExport\CustomsDeclare\_PopupConfirmOrder.cshtml" />
    <Content Include="Views\RegisterVesselExport\Contract.cshtml" />
    <Content Include="Views\CheckIn\_RegisterShipment.cshtml" />
    <Content Include="Views\RegisterVesselExport\BookingNumber\_PopupContainerRegistered.cshtml" />
    <Content Include="Views\RegisterVesselExport\CustomsDeclare\_PopupContainerRegistered.cshtml" />
    <Content Include="Views\Checkout\Receipt.cshtml" />
    <Content Include="Views\CustomsDeclaration\Inyard.cshtml" />
    <Content Include="Views\Eir\Print.cshtml" />
    <Content Include="Views\Customs39\CustomsDeclarationViaConfig\Edit\_EditContainerSelected.cshtml" />
    <Content Include="Views\Customs39\CustomsDeclarationViaConfig\Edit\_EditCustomDeclaration.cshtml" />
    <Content Include="Views\Customs39\CustomsDeclarationViaConfig\_ConfirmModal.cshtml" />
    <Content Include="Views\Customs39\CustomsDeclarationViaConfig\_Create.cshtml" />
    <Content Include="Views\Customs39\CustomsDeclarationViaConfig\_Edit.cshtml" />
    <Content Include="Views\Customs39\CustomsDeclarationViaConfig\_ImportExcel.cshtml" />
    <Content Include="Views\Customs39\CustomsDeclarationViaConfig\_ImportExcelCustomDeclarationList.cshtml" />
    <Content Include="Views\Customs39\CustomsDeclarationViaConfig\_Index.cshtml" />
    <Content Include="Views\PaymentRequest\EmailConfig.cshtml" />
    <Content Include="Views\PaymentRequest\Index.cshtml" />
    <Content Include="Views\PaymentRequest\Detail.cshtml" />
    <Content Include="Views\PaymentRequest\Detail\_General.cshtml" />
    <Content Include="Views\PaymentRequest\Detail\_Detail.cshtml" />
    <Content Include="Views\PaymentRequest\History.cshtml" />
    <Content Include="Views\Shared\_Chatbot.cshtml" />
    <Content Include="Views\RegisterVesselExport\ReportGNL.cshtml" />
    <Content Include="Views\Commodity\Index.cshtml" />
    <Content Include="Views\Commodity\_CommodityInfo.cshtml" />
    <Content Include="Views\Commodity\_CommodityDeclaration.cshtml" />
    <Content Include="Views\Commodity\_CommodityChoice.cshtml" />
    <Content Include="Views\Commodity\_BulkEdit.cshtml" />
    <Content Include="Views\FullContainerDelivery\_ImoUnnoInfo.cshtml" />
    <Content Include="Views\Customs39\CollateWithCfg.cshtml" />
    <Content Include="Views\Registration\PaymentList\_PopupSendNoticeErr.cshtml" />
    <Content Include="Views\Invoices\InvoiceAdjustment.cshtml" />
    <Content Include="Views\Invoices\PreviewInvoice.cshtml" />
    <Content Include="Views\Invoices\InvoicePartials\_PopupNoticeErr.cshtml" />
    <Content Include="Views\Invoices\InvoicePartials\_PopupSendNoticeErr.cshtml" />
    <Content Include="Views\Invoices\PreviewInvoiceTCI.cshtml" />
    <Content Include="Views\Invoices\InvoicePartials\_PopupListInvoiceAdjusted.cshtml" />
    <Content Include="Views\ServiceAttachment\Index.cshtml" />
    <Content Include="Views\ServiceAttachment\_Register.cshtml" />
    <Content Include="Views\ServiceAttachment\_PopupChoiceService.cshtml" />
    <Content Include="Views\ServiceAttachment\_PopupError.cshtml" />
    <Content Include="Views\ServiceAttachment\_PopupConfirmCancel.cshtml" />
    <Content Include="Views\Registration\PaymentList\Partial\_IndexNormal.cshtml" />
    <Content Include="Views\Registration\PaymentList\Partial\_IndexCTL.cshtml" />
    <Content Include="Views\Invoices\InvoicePartials\_InvoiceCancel.cshtml" />
    <Content Include="Views\Invoices\InvoicePartials\_InvoiceCancelCTL.cshtml" />
    <Content Include="Views\Invoices\Adjustment\InvoiceAdjustment.cshtml" />
    <Content Include="Views\Invoices\InvoicePartials\_InvoiceAdjustmentCTL.cshtml" />
    <Content Include="Views\Invoices\InvoicePartials\_ReplaceInvoiceCTL.cshtml" />
    <Content Include="Views\FullContainerDelivery\_EditBookFpodInfo.cshtml" />
    <Content Include="Views\FullContainerDelivery\_ErrorBookFpodInfo.cshtml" />
    <Content Include="Views\FullContainerDelivery\_ErrorPopup.cshtml" />
    <Content Include="Views\Home\_PopupAddEmailManagement.cshtml" />
    <Content Include="Views\Home\_PopupChangePwd.cshtml" />
    <Content Include="Views\Home\_PopupWarningChangePwd.cshtml" />
    <Content Include="Views\Home\_PopupWarningChangePwdChangeSite.cshtml" />
    <Content Include="Views\ConnectionManagement\Index.cshtml" />
    <Content Include="Views\ConnectionManagement\Connected\_Index.cshtml" />
    <Content Include="Views\ConnectionManagement\Connected\_PopupEditRemark.cshtml" />
    <Content Include="Views\ConnectionManagement\Connected\_PopupCreateOrEditGroupConnect.cshtml" />
    <Content Include="Views\ConnectionManagement\Connected\_PopupAddMemberToGroup.cshtml" />
    <Content Include="Views\ConnectionManagement\ConnectionGroup\_Index.cshtml" />
    <Content Include="Views\ConnectionManagement\WaitingAccept\_index.cshtml" />
    <Content Include="Views\ConnectionManagement\WaitingAccept\_PopupConfirmAcceptConnect.cshtml" />
    <Content Include="Views\CheckIn\EditCheckInAccount.cshtml" />
    <Content Include="Views\HazadousMasterInfo\Index.cshtml" />
    <Content Include="Views\EmptyContainerDelivery\Lookup.cshtml" />
    <Content Include="Views\EmptyContainerDelivery\_PopupEmptyReceiDetail.cshtml" />
    <Content Include="Views\EmptyContainerDelivery\_PopupDetail.cshtml" />
    <Content Include="Views\EdiEmptyDelivery\Index.cshtml" />
    <Content Include="Views\EdiEmptyDelivery\_AddOrEditEdiCoparn.cshtml" />
    <Content Include="Views\EdiEmptyDelivery\_ImportExcel.cshtml" />
    <Content Include="Views\EmptyContainerDelivery\_SpecHdlPop.cshtml" />
    <Content Include="Views\EmptyContainerDelivery\_Register-site-optional.cshtml" />
    <Content Include="Views\EmptyContainerDelivery\_ListOrderDetail-site-optional.cshtml" />
    <Content Include="Views\EmptyContainerDelivery\_ErrorPopup.cshtml" />
    <Content Include="Views\PaymentList\_ErrorPopup.cshtml" />
    <Content Include="Views\EmptyContainerDelivery\_PopupSelectEmptyRecv.cshtml" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Snp.Cache\Snp.Cache.csproj">
      <Project>{9aa5da68-e6de-4c72-9010-8fa977effde3}</Project>
      <Name>Snp.Cache</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.ePort.Common\Snp.ePort.Common.csproj">
      <Project>{f58832de-5553-42c9-8566-8120535248a8}</Project>
      <Name>Snp.ePort.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.ePort.Core\Snp.ePort.Core.csproj">
      <Project>{ff3110c1-eea8-4f5b-a448-3bde77cd87ca}</Project>
      <Name>Snp.ePort.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.ePort.Entity\Snp.ePort.Entity.csproj">
      <Project>{910c50b0-22e2-415c-97de-435bb67a47e1}</Project>
      <Name>Snp.ePort.Entity</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.ePort.Gateway.Impl\Snp.ePort.Gateway.Impl.csproj">
      <Project>{0afaf605-c505-4c78-8e24-4f9f84e2f462}</Project>
      <Name>Snp.ePort.Gateway.Impl</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.ePort.Gateway\Snp.ePort.Gateway.csproj">
      <Project>{9f1b7a5a-c632-4917-8743-070ff43bf846}</Project>
      <Name>Snp.ePort.Gateway</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.ePort.Report\Snp.ePort.Report.csproj">
      <Project>{28ee7921-f456-4d13-a802-b5959c4eac84}</Project>
      <Name>Snp.ePort.Report</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.ePort.Repository\Snp.ePort.Repository.csproj">
      <Project>{CEB0D04B-4E02-4BC9-A988-E937892459E9}</Project>
      <Name>Snp.ePort.Repository</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.ePort.Service\Snp.ePort.Service.csproj">
      <Project>{EF17AC6E-FCAB-4C99-9998-D87CCCF81641}</Project>
      <Name>Snp.ePort.Service</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.ePort.WsExternal\Snp.ePort.WsExternal.csproj">
      <Project>{252dcfb4-e150-4514-a9b6-400e08f75160}</Project>
      <Name>Snp.ePort.WsExternal</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.Log\Snp.Log.csproj">
      <Project>{4cb088cd-f669-4176-869c-95eb27348845}</Project>
      <Name>Snp.Log</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.Tos.Entity\Snp.Tos.Entity.csproj">
      <Project>{62d4bfe6-7de9-44ec-a16d-f15a2a894235}</Project>
      <Name>Snp.Tos.Entity</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{4A0DDDB5-7A95-4FBF-97CC-616D07737A77}" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\MSGResource.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets')" />
  <!--<Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets')" />-->
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>43724</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:43724/</IISUrl>
          <OverrideIISAppRootUrl>True</OverrideIISAppRootUrl>
          <IISAppRootUrl>http://localhost:43724/</IISAppRootUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
      <UserProperties DismissedDevExtremeVersion="********" />
    </VisualStudio>
  </ProjectExtensions>
  <UsingTask TaskName="TransformXml" AssemblyFile="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v14.0\Web\Microsoft.Web.Publishing.Tasks.dll" />
  <Target Name="ApplyTransform" Condition="Exists('Web.$(Configuration).config')">
    <TransformXml Source="web.config" Transform="Web.$(Configuration).config" Destination="Web.config" />
  </Target>
  <Target Name="BeforeBuild">
    <Exec Command="attrib -r Web.config" />
    <CallTarget Targets="ApplyTransform" />
  </Target>
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
  </PropertyGroup>
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\packages\MSBuild.Microsoft.VisualStudio.Web.targets.********\build\MSBuild.Microsoft.VisualStudio.Web.targets.props')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\MSBuild.Microsoft.VisualStudio.Web.targets.********\build\MSBuild.Microsoft.VisualStudio.Web.targets.props'))" />
    <Error Condition="!Exists('..\..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
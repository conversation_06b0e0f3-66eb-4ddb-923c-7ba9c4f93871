using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using Newtonsoft.Json;
using RestSharp;

namespace Snp.ePort.Web.Infrastructure.Services
{
    public class MSDSGatewayService : IMSDSGatewayService
    {
        private readonly string _gatewayApiBaseUrl;
        private readonly RestClient _restClient;

        public MSDSGatewayService()
        {
            _gatewayApiBaseUrl = ConfigurationManager.AppSettings["ApiGatewayBaseUrl"] ?? "http://localhost:22674";
            _restClient = new RestClient(_gatewayApiBaseUrl);
        }

        public async Task<MSDSUploadGatewayResponse> UploadMSDSFilesAsync(List<HttpPostedFileBase> files, List<string> orderDetailNos)
        {
            try
            {
                var request = new RestRequest("api/msds/upload", Method.POST);
                
                // Add order detail numbers as form parameter
                request.AddParameter("orderDetailNos", string.Join(",", orderDetailNos));

                // Add files
                foreach (var file in files)
                {
                    var fileBytes = new byte[file.ContentLength];
                    file.InputStream.Position = 0; // Reset stream position
                    file.InputStream.Read(fileBytes, 0, file.ContentLength);
                    request.AddFile("files", fileBytes, file.FileName, file.ContentType);
                }

                var response = await _restClient.ExecuteTaskAsync(request);
                
                if (response.IsSuccessful)
                {
                    return JsonConvert.DeserializeObject<MSDSUploadGatewayResponse>(response.Content);
                }
                else
                {
                    return new MSDSUploadGatewayResponse
                    {
                        Success = false,
                        Message = $"Gateway API error: {response.ErrorMessage ?? response.StatusDescription}"
                    };
                }
            }
            catch (Exception ex)
            {
                return new MSDSUploadGatewayResponse
                {
                    Success = false,
                    Message = $"Error calling Gateway API: {ex.Message}"
                };
            }
        }

        public async Task<MSDSDeleteGatewayResponse> DeleteMSDSFileAsync(string fileId)
        {
            try
            {
                var request = new RestRequest($"api/msds/delete/{fileId}", Method.DELETE);
                var response = await _restClient.ExecuteTaskAsync(request);
                
                if (response.IsSuccessful)
                {
                    return JsonConvert.DeserializeObject<MSDSDeleteGatewayResponse>(response.Content);
                }
                else
                {
                    return new MSDSDeleteGatewayResponse
                    {
                        Success = false,
                        Message = $"Gateway API error: {response.ErrorMessage ?? response.StatusDescription}"
                    };
                }
            }
            catch (Exception ex)
            {
                return new MSDSDeleteGatewayResponse
                {
                    Success = false,
                    Message = $"Error calling Gateway API: {ex.Message}"
                };
            }
        }

        public async Task<List<MSDSFileGatewayInfo>> GetMSDSFilesByOrderDetailNoAsync(string orderDetailNo)
        {
            try
            {
                var request = new RestRequest($"api/msds/files/{orderDetailNo}", Method.GET);
                var response = await _restClient.ExecuteTaskAsync(request);
                
                if (response.IsSuccessful)
                {
                    return JsonConvert.DeserializeObject<List<MSDSFileGatewayInfo>>(response.Content);
                }
                else
                {
                    return new List<MSDSFileGatewayInfo>();
                }
            }
            catch (Exception)
            {
                return new List<MSDSFileGatewayInfo>();
            }
        }
    }
}

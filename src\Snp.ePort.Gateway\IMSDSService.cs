using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web;

namespace Snp.ePort.Gateway
{
    public interface IMSDSService
    {
        /// <summary>
        /// Upload MSDS files to MinIO and save metadata to database
        /// </summary>
        /// <param name="request">Upload request containing files and order detail numbers</param>
        /// <returns>Upload response with file information</returns>
        Task<MSDSUploadResponse> UploadMSDSFilesAsync(MSDSUploadRequest request, MSDSConfigInfo config);

        /// <summary>
        /// Delete MSDS file from MinIO and database
        /// </summary>
        /// <param name="fileId">File ID to delete</param>
        /// <returns>Delete response</returns>
        Task<MSDSDeleteResponse> DeleteMSDSFileAsync(string fileId);

        /// <summary>
        /// Get MSDS files by order detail number
        /// </summary>
        /// <param name="orderDetailNo">Order detail number</param>
        /// <returns>List of MSDS files</returns>
        Task<List<MSDSFileInfo>> GetMSDSFilesByOrderDetailNoAsync(string orderDetailNo);
    }

    // Request/Response models
    public class MSDSUploadRequest
    {
        public List<string> OrderDetailNos { get; set; }
        public List<HttpPostedFile> Files { get; set; }

        public MSDSUploadRequest()
        {
            OrderDetailNos = new List<string>();
            Files = new List<HttpPostedFile>();
        }
    }

    public class MSDSUploadResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public List<string> Errors { get; set; }
        public List<MSDSUploadedFileInfo> UploadedFiles { get; set; }

        public MSDSUploadResponse()
        {
            UploadedFiles = new List<MSDSUploadedFileInfo>();
        }
    }

    public class MSDSDeleteResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
    }

    public class MSDSUploadedFileInfo
    {
        public string FileId { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public long FileSize { get; set; }
        public string ContentType { get; set; }
    }

    public class MSDSFileInfo
    {
        public string FileId { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public long FileSize { get; set; }
        public string ContentType { get; set; }
        public System.DateTime UploadDate { get; set; }
    }

    public class MSDSConfigInfo
    {
        public int MaxFilesPerContainer { get; set; }
        public long MaxTotalSizePerContainer { get; set; }
        public List<string> AllowedExtensions { get; set; }

        public MSDSConfigInfo()
        {
            AllowedExtensions = new List<string>();
        }
    }

    // Infrastructure interfaces
    public interface IMinIOService
    {
        Task<MinIOUploadResult> UploadFileAsync(string filePath, System.IO.Stream fileStream, string contentType);
        Task<bool> DeleteFileAsync(string filePath);
        Task<System.IO.Stream> GetFileAsync(string filePath);
    }

    // Supporting classes
    public class MinIOUploadResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public string FilePath { get; set; }
    }

    public class MSDSFileMetadata
    {
        public string FileId { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public long FileSize { get; set; }
        public string ContentType { get; set; }
        public System.DateTime UploadDate { get; set; }
        public string OrderDetailNo { get; set; }
    }

    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
    }

    public class MSDSContainerViewModel
    {
        public string ContainerNo { get; set; }
        public string RegisterNo { get; set; }
        public string OrderDetailNo { get; set; }
        public string BookingNo { get; set; }
        public string IMO { get; set; }
        public string UNNO { get; set; }
        public int IMO_UNNO_Count { get; set; }
        public int MSDS_Count { get; set; }
        public List<MSDSFileInfo> UploadedFiles { get; set; }

        public MSDSContainerViewModel()
        {
            UploadedFiles = new List<MSDSFileInfo>();
        }
    }

    public class MSDSFileUpload
    {
        public string FileName { get; set; }
        public byte[] FileContent { get; set; }
        public long FileSize { get; set; }
        public string FileExtension { get; set; }
    }

    public class MSDSValidationConfig
    {
        public int MaxFilesPerContainer { get; set; } = 5;
        public long MaxTotalSizePerContainer { get; set; } = 10 * 1024 * 1024; // 10MB
        public string[] AllowedExtensions { get; set; } = { ".pdf", ".txt", ".doc", ".docx", ".xls", ".xlsx" };
        public string AllowedExtensionsDisplay => string.Join(", ", AllowedExtensions);
    }
}

@{
    ViewBag.Title = "Khai báo MSDS";

    var maxFileUpload = ViewBag.maxFileUpload ?? "5"; // để mặc định nếu không có
    var allowFileTypes = ViewBag.allowFileTypes ?? "*.pdf, *.txt, *.doc, *.docx, *.xls, *.xlsx"; // để mặc định nếu không có
    var maxFileSize = ViewBag.maxFileSize ?? "10"; // để mặc định nếu không có
}

<style>
    /* MSDS Upload Container */
    .msds-upload-container {
        padding: 8px;
        min-height: 60px;
    }

    /* Upload Button Area */
    .upload-button-area {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 12px;
        border: 1px dashed #007bff;
        border-radius: 4px;
        background-color: #f8f9fa;
        cursor: pointer;
        transition: all 0.2s ease;
        margin-bottom: 8px;
    }

    .upload-button-area:hover {
        background-color: #e3f2fd;
        border-color: #0056b3;
    }

    .upload-button-area.drag-over {
        background-color: #d4edda;
        border-color: #28a745;
        border-style: solid;
    }

    .upload-text {
        color: #007bff;
        font-size: 13px;
        margin-left: 6px;
        font-weight: 500;
    }

    /* Uploaded Files */
    .uploaded-files {
        margin-top: 4px;
    }

    .file-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 6px 8px;
        margin-bottom: 4px;
        background-color: #fff;
        border: 1px solid #dee2e6;
        border-radius: 3px;
        font-size: 12px;
    }

    .file-info {
        display: flex;
        align-items: center;
        flex: 1;
    }

    .file-icon {
        color: #dc3545;
        margin-right: 6px;
        font-size: 12px;
    }

    .file-name {
        color: #007bff;
        text-decoration: underline;
        cursor: pointer;
        margin-right: 4px;
    }

    .file-size {
        color: #6c757d;
        font-size: 11px;
    }

    .remove-file-btn {
        background: none;
        border: none;
        color: #dc3545;
        cursor: pointer;
        padding: 2px 4px;
        font-size: 12px;
    }

    .remove-file-btn:hover {
        color: #c82333;
    }

    /* Upload Progress */
    .upload-progress {
        display: flex;
        align-items: center;
        margin-top: 8px;
        padding: 4px 0;
    }

    .progress-bar-container {
        flex: 1;
        height: 4px;
        background-color: #e9ecef;
        border-radius: 2px;
        margin-right: 8px;
        overflow: hidden;
    }

    .progress-bar-fill {
        height: 100%;
        background-color: #007bff;
        width: 0%;
        transition: width 0.3s ease;
    }

    .progress-text {
        font-size: 11px;
        color: #6c757d;
        min-width: 30px;
    }

    /* DataGrid Styles */
    #msdsTable th {
        background-color: #f8f9fa;
        font-weight: 600;
        border-bottom: 2px solid #dee2e6;
    }

    #msdsTable td {
        vertical-align: middle;
    }

    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .btn-group-actions {
        gap: 10px;
    }

    .alert-info-custom {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border: 1px solid #2196f3;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
</style>


<div class="container-fluid">  
    <div class="row">
        <div class="col-12">
            @(Html.DevExtreme().DataGrid()
    .ID("lstContainerDangerous")
    .DataSource(new List<object>()) // hoặc gán khi load
    .KeyExpr("ORDER_DETAIL_ID")
    .ShowBorders(true)
    .ColumnAutoWidth(true)
    .Selection(s =>
    {
        s.Mode(SelectionMode.Multiple);
        s.ShowCheckBoxesMode(GridSelectionShowCheckBoxesMode.Always);
        s.AllowSelectAll(true);
    })
    .Columns(columns =>
    {
        columns.Add().DataField("ITEM_NO").Caption("Số Container").Alignment(HorizontalAlignment.Center);
        columns.Add().DataField("ORDER_DETAIL_NO").Caption("Số đăng ký").Alignment(HorizontalAlignment.Center);
        columns.Add().DataField("BOOKING_NO").Caption("Số booking").Alignment(HorizontalAlignment.Center);
        columns.Add().DataField("HAZADOUS").Caption("IMO").Alignment(HorizontalAlignment.Center);
        columns.Add().DataField("UNNO").Caption("UNNO").Alignment(HorizontalAlignment.Center);
        columns.Add().DataField("HAZADOUS_COUNT").Caption("Số cặp IMO/UNNO").Alignment(HorizontalAlignment.Center);
        columns.Add()
            .Caption("Số Lượng MSDS đã import")
            .CalculateCellValue("function(data) { return 0; }")
            .Alignment(HorizontalAlignment.Center);

        columns.Add()
            .Caption("Tải lên MSDS")
            .Alignment(HorizontalAlignment.Center)
            .Width(300)
            .CellTemplate("msdsUploadTemplate");
    }))
        </div>
    </div>
</div>

<div class="d-flex justify-content-between align-items-start mt-3 flex-wrap">
    <div class="text-danger mb-2" style="max-width: 65%;">
        <b>Lưu ý khi khai báo MSDS:</b><br />
        - Container chứa hàng nguy hiểm bắt buộc phải đính kèm file MSDS khi giao hàng vào cảng.<br />
        - Mỗi cặp IMO/UNNO cần có ít nhất 1 file MSDS tương ứng.<br />
        - Chỉ chấp nhận các file định dạng: <span class="text-danger mb-2">@allowFileTypes</span><br />
        - Tổng dung lượng file MSDS cho mỗi container không vượt quá <span class="text-danger mb-2">@maxFileUpload MB</span>.
    </div>

    <div class="text-end">
        <button id="importBtn" class="btn btn-info me-2 mb-2">Import MSDS</button>
        <button id="saveBtn" class="btn btn-primary mb-2">Lưu</button>
    </div>
</div>

<!-- Template cho upload cell -->
<script type="text/html" id="msdsUploadTemplate">
    <div class="msds-upload-container" data-container="<%= data.ORDER_DETAIL_NO %>">
        <!-- Upload Button -->
        <div class="upload-button-area">
            <i class="fas fa-cloud-upload-alt" style="color: #007bff; font-size: 14px;"></i>
            <span class="upload-text">Nhấn hoặc kéo thả để thêm file</span>
            <input type="file" class="file-input" multiple accept=".pdf,.txt,.doc,.docx,.xls,.xlsx" style="display: none;" />
        </div>

        <!-- Uploaded Files List -->
        <div class="uploaded-files"></div>

        <!-- Upload Progress -->
        <div class="upload-progress" style="display: none;">
            <div class="progress-bar-container">
                <div class="progress-bar-fill"></div>
            </div>
            <span class="progress-text">0%</span>
        </div>
    </div>
</script>

@section Scripts {
    <script>
        // Khai báo biến global từ ViewBag
        var maxFileUpload = @maxFileUpload;
        var allowFileTypes = '@allowFileTypes';
        var maxFileSize = @maxFileSize * 1024 * 1024; // Convert MB to bytes

        $(document).ready(function () {
            // Load dữ liệu cho DataGrid
            loadContainerData();

            // Khởi tạo upload handlers sau khi DataGrid render
            setTimeout(function() {
                initializeMSDSUpload();
            }, 1000);

            // Xử lý nút Import MSDS
            $('#importBtn').on('click', function () {
                uploadSelectedFiles();
            });

            // Xử lý nút Lưu
            $('#saveBtn').on('click', function () {
                saveAllChanges();
            });
        });

        function loadContainerData() {
            // Load dữ liệu container từ server
            $.ajax({
                url: '/MSDS/GetContainerList',
                type: 'GET',
                success: function(data) {
                    const dataGrid = $("#lstContainerDangerous").dxDataGrid("instance");
                    dataGrid.option("dataSource", data);
                },
                error: function() {
                    showError('Không thể tải danh sách container');
                }
            });
        }

        function initializeMSDSUpload() {
            // Ngăn chặn default drag behavior
            $(document).on('dragover drop', function(e) {
                e.preventDefault();
            });

            // Click để chọn file
            $(document).off('click', '.upload-button-area').on('click', '.upload-button-area', function (e) {
                e.stopPropagation();
                $(this).find('.file-input').click();
            });

            // Xử lý khi chọn file
            $(document).off('change', '.file-input').on('change', '.file-input', function () {
                handleFileSelection(this);
            });

            // Drag & Drop events
            $(document).off('dragenter', '.upload-button-area').on('dragenter', '.upload-button-area', function (e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('drag-over');
            });

            $(document).off('dragover', '.upload-button-area').on('dragover', '.upload-button-area', function (e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('drag-over');
            });

            $(document).off('dragleave', '.upload-button-area').on('dragleave', '.upload-button-area', function (e) {
                e.preventDefault();
                e.stopPropagation();
                if (!$(this)[0].contains(e.relatedTarget)) {
                    $(this).removeClass('drag-over');
                }
            });

            $(document).off('drop', '.upload-button-area').on('drop', '.upload-button-area', function (e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('drag-over');

                const files = e.originalEvent.dataTransfer.files;
                if (files && files.length > 0) {
                    const input = $(this).find('.file-input')[0];
                    if (input) {
                        input.files = files;
                        handleFileSelection(input);
                    }
                }
            });

            // Xử lý xóa file
            $(document).off('click', '.remove-file-btn').on('click', '.remove-file-btn', function (e) {
                e.stopPropagation();
                $(this).closest('.file-item').remove();
            });
        }

        function handleFileSelection(input) {
            const container = $(input).closest('.msds-upload-container').data('container');
            const files = Array.from(input.files);
            const uploadedFilesDiv = $(input).closest('.msds-upload-container').find('.uploaded-files');

            // Validate files
            const validation = validateFiles(files);
            if (!validation.isValid) {
                showError(validation.errors.join('<br>'));
                input.value = ''; // Clear input
                return;
            }

            // Hiển thị danh sách file
            displayFileList(uploadedFilesDiv, files, container);
        }

        function validateFiles(files) {
            const result = { isValid: true, errors: [] };

            // Kiểm tra số lượng file
            if (files.length > maxFileUpload) {
                result.isValid = false;
                result.errors.push(`Chỉ được chọn tối đa ${maxFileUpload} file cùng lúc.`);
            }

            // Kiểm tra định dạng file
            const allowedExtensions = allowFileTypes.split(',').map(ext => ext.trim());
            const invalidFiles = files.filter(file => {
                const ext = '.' + file.name.split('.').pop().toLowerCase();
                return !allowedExtensions.includes(ext);
            });

            if (invalidFiles.length > 0) {
                result.isValid = false;
                result.errors.push(`Hệ thống chỉ chấp nhận các định dạng file sau: ${allowFileTypes}. Vui lòng kiểm tra định dạng trước khi tải lên.`);
            }

            // Kiểm tra tổng dung lượng
            const totalSize = files.reduce((sum, file) => sum + file.size, 0);
            if (totalSize > maxFileSize) {
                result.isValid = false;
                const maxSizeMB = (maxFileSize / 1024 / 1024).toFixed(0);
                result.errors.push(`Tổng dung lượng file MSDS cho 1 container tối đa ${maxSizeMB}MB. Vui lòng kiểm tra trước khi tải lên.`);
            }

            return result;
        }

        function displayFileList(uploadedFilesDiv, files, container) {
            // Thêm files vào danh sách hiện có thay vì thay thế
            files.forEach((file, index) => {
                const fileSize = formatFileSize(file.size);
                const fileItem = $(`
                    <div class="file-item" data-file-name="${file.name}">
                        <div class="file-info">
                            <i class="fas fa-file-pdf file-icon"></i>
                            <span class="file-name">${file.name}</span>
                            <span class="file-size">(${fileSize})</span>
                        </div>
                        <button type="button" class="remove-file-btn" title="Xóa file">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `);

                uploadedFilesDiv.append(fileItem);
            });
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }



        function uploadSelectedFiles() {
            // Lấy các row được chọn từ DataGrid
            const dataGrid = $("#lstContainerDangerous").dxDataGrid("instance");
            const selectedRowKeys = dataGrid.getSelectedRowKeys();

            if (selectedRowKeys.length === 0) {
                showError('Vui lòng chọn ít nhất một container để upload MSDS.');
                return;
            }

            const uploadPromises = [];

            selectedRowKeys.forEach(function (rowKey) {
                const rowData = dataGrid.getDataSource().items().find(item => item.ORDER_DETAIL_ID === rowKey);
                if (rowData) {
                    const container = rowData.ORDER_DETAIL_NO;
                    const uploadContainer = $(`.msds-upload-container[data-container="${container}"]`);
                    const input = uploadContainer.find('.file-input')[0];

                    if (input && input.files.length > 0) {
                        uploadPromises.push(uploadFilesForContainer(container, input.files, uploadContainer));
                    }
                }
            });

            if (uploadPromises.length === 0) {
                showError('Không có file nào được chọn để upload.');
                return;
            }

            Promise.all(uploadPromises)
                .then(results => {
                    const successCount = results.filter(r => r.success).length;
                    const totalCount = results.length;

                    if (successCount === totalCount) {
                        showSuccess(`Upload thành công ${successCount} container.`);
                        // Refresh DataGrid
                        dataGrid.refresh();
                    } else {
                        showWarning(`Upload thành công ${successCount}/${totalCount} container. Vui lòng kiểm tra lại.`);
                    }
                })
                .catch(error => {
                    showError('Có lỗi xảy ra trong quá trình upload: ' + error.message);
                });
        }

        function uploadFilesForContainer(container, files, uploadContainer) {
            return new Promise((resolve, reject) => {
                const formData = new FormData();
                formData.append('containerNo', container);

                Array.from(files).forEach((file, index) => {
                    formData.append(`files`, file);
                });

                // Hiển thị progress
                const progressDiv = uploadContainer.find('.upload-progress');
                const progressBarFill = progressDiv.find('.progress-bar-fill');
                const progressText = progressDiv.find('.progress-text');
                progressDiv.show();

                $.ajax({
                    url: '/MSDS/UploadFiles',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    xhr: function () {
                        const xhr = new window.XMLHttpRequest();
                        xhr.upload.addEventListener("progress", function (evt) {
                            if (evt.lengthComputable) {
                                const percentComplete = Math.round((evt.loaded / evt.total) * 100);
                                progressBarFill.css('width', percentComplete + '%');
                                progressText.text(percentComplete + '%');
                            }
                        }, false);
                        return xhr;
                    },
                    success: function (response) {
                        progressDiv.hide();
                        if (response.Success) {
                            resolve({ success: true, container: container });
                            // Clear input và reset upload area
                            uploadContainer.find('.file-input').val('');
                            // Không xóa file list để user thấy file đã upload
                        } else {
                            showError(`Container ${container}: ${response.Message}`);
                            resolve({ success: false, container: container, error: response.Message });
                        }
                    },
                    error: function (xhr, status, error) {
                        progressDiv.hide();
                        const errorMsg = xhr.responseJSON?.Message || error || 'Upload thất bại';
                        showError(`Container ${container}: ${errorMsg}`);
                        resolve({ success: false, container: container, error: errorMsg });
                    }
                });
            });
        }

        function saveAllChanges() {
            // Implement save logic if needed
            showSuccess('Đã lưu thành công!');
        }

        function showError(msg) {
            // Sử dụng thư viện notification có sẵn trong dự án ePort
            if (typeof message !== 'undefined' && message.error) {
                message.error(msg);
            } else if (typeof toastr !== 'undefined') {
                toastr.error(msg);
            } else {
                alert('Lỗi: ' + msg);
            }
        }

        function showSuccess(msg) {
            if (typeof message !== 'undefined' && message.success) {
                message.success(msg);
            } else if (typeof toastr !== 'undefined') {
                toastr.success(msg);
            } else {
                alert('Thành công: ' + msg);
            }
        }

        function showWarning(msg) {
            if (typeof message !== 'undefined' && message.warning) {
                message.warning(msg);
            } else if (typeof toastr !== 'undefined') {
                toastr.warning(msg);
            } else {
                alert('Cảnh báo: ' + msg);
            }
        }


    </script>
}
